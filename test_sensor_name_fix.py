#!/usr/bin/env python3
"""
Test script to verify the sensor name sanitization fix.

This script tests the new sanitize_entity_name function to ensure
it properly handles zone names with apostrophes and other special characters.
"""

import sys
import os
import re

# Add the aicleaner directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aicleaner'))

def sanitize_entity_name(name: str) -> str:
    """
    Sanitizes zone names for Home Assistant entity IDs.
    
    Converts zone names like "Rowan's Room" to "rowan_s_room" to prevent
    URL encoding issues with special characters in Home Assistant API calls.
    
    Args:
        name: The zone name to sanitize
        
    Returns:
        A sanitized string safe for use in Home Assistant entity IDs
    """
    # Convert to lowercase
    sanitized = name.lower()
    
    # Replace apostrophes and other special characters with underscores
    # This handles cases like "Rowan's Room" -> "rowan_s_room"
    sanitized = re.sub(r"[^\w\s-]", "_", sanitized)
    
    # Replace spaces and hyphens with underscores
    sanitized = re.sub(r"[\s-]+", "_", sanitized)
    
    # Remove consecutive underscores
    sanitized = re.sub(r"_+", "_", sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip("_")
    
    return sanitized

def test_sensor_name_sanitization():
    """Test the sensor name sanitization function."""
    
    test_cases = [
        # (input, expected_output, description)
        ("Rowan's Room", "rowan_s_room", "Apostrophe handling"),
        ("Living Room", "living_room", "Space handling"),
        ("Kitchen", "kitchen", "Simple name"),
        ("Master Bedroom", "master_bedroom", "Multiple spaces"),
        ("Kid's Playroom", "kid_s_playroom", "Apostrophe in middle"),
        ("Office/Study", "office_study", "Slash handling"),
        ("Dining-Room", "dining_room", "Hyphen handling"),
        ("Bathroom #1", "bathroom_1", "Hash symbol handling"),
        ("Guest Room (Upstairs)", "guest_room_upstairs", "Parentheses handling"),
        ("Mom & Dad's Room", "mom_dad_s_room", "Multiple special chars"),
    ]
    
    print("🧪 Testing Sensor Name Sanitization Fix")
    print("=" * 50)
    
    all_passed = True
    
    for input_name, expected, description in test_cases:
        result = sanitize_entity_name(input_name)
        sensor_id = f"sensor.aicleaner_{result}_tasks"
        
        passed = result == expected
        status = "✅ PASS" if passed else "❌ FAIL"
        
        print(f"{status} {description}")
        print(f"  Input:    '{input_name}'")
        print(f"  Expected: '{expected}'")
        print(f"  Got:      '{result}'")
        print(f"  Sensor:   '{sensor_id}'")
        print()
        
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All tests PASSED! The sensor naming fix is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Restart the AICleaner addon")
        print("2. Check the addon logs for 400 errors (should be gone)")
        print("3. Test the Lovelace card UI")
    else:
        print("❌ Some tests FAILED. Please review the sanitization logic.")
    
    return all_passed

if __name__ == "__main__":
    test_sensor_name_sanitization()
