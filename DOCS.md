# AICleaner v2.0+ Documentation

Complete documentation for the AICleaner Home Assistant Add-on.

## 📋 Table of Contents

- [Installation](#-installation)
- [Configuration](#-configuration)
- [Features](#-features)
- [Troubleshooting](#-troubleshooting)
- [API Reference](#-api-reference)
- [Advanced Usage](#-advanced-usage)

## 🚀 Installation

### Prerequisites

Before installing AICleaner, ensure you have:

- **Home Assistant OS, Supervised, or Container** (version 2023.1 or later)
- **At least one camera entity** configured in Home Assistant
- **Google Gemini API key** ([Get one free here](https://aistudio.google.com/app/apikey))
- **512MB RAM** available (1GB recommended for optimal performance)

### Step-by-Step Installation

1. **Add the Repository**
   ```
   Home Assistant → Settings → Add-ons → Add-on Store → ⋮ Menu → Repositories
   Add: https://github.com/sporebattyl/Aiclean
   ```

2. **Install the Add-on**
   - Find "AICleaner v2.0+" in the add-on store
   - Click **Install** (installation takes 2-3 minutes)
   - Wait for the installation to complete

3. **Configure the Add-on**
   - Go to the **Configuration** tab
   - Add your Gemini API key
   - Configure at least one zone (see Configuration section)

4. **Start the Add-on**
   - Click **Start** and enable "Start on boot"
   - Check the **Log** tab for successful initialization
   - Look for "AICleaner initialized successfully" message

## ⚙️ Configuration

### Basic Configuration

The minimum configuration requires:

```yaml
gemini_api_key: "your_gemini_api_key_here"
display_name: "Your Name"
zones:
  - name: "kitchen"
    camera_entity: "camera.kitchen"
    todo_list_entity: "todo.kitchen_tasks"
    notification_service: "mobile_app_your_phone"
```

### Zone Configuration

Each zone requires these essential settings:

| Setting | Description | Example |
|---------|-------------|---------|
| `name` | Unique zone identifier | `"kitchen"` |
| `camera_entity` | HA camera entity ID | `"camera.kitchen"` |
| `todo_list_entity` | HA todo list entity ID | `"todo.kitchen_tasks"` |
| `notification_service` | HA notification service | `"mobile_app_your_phone"` |

### Advanced Configuration Options

#### AI & Analysis Settings
```yaml
# Multi-model AI support
enable_multi_model_ai: false
claude_api_key: ""  # Optional
openai_api_key: ""  # Optional

# Advanced AI features
enable_predictive_analytics: true
enable_scene_understanding: true
```

#### Mobile Integration Settings
```yaml
# Mobile optimization
enable_mobile_integration: true
enable_pwa_features: true
mobile_push_notifications: true
mobile_theme_preference: "auto"  # auto, light, dark
mobile_compact_mode: false
mobile_gesture_controls: true
```

#### Gamification Settings
```yaml
# Gamification features
enable_gamification: true
gamification_achievements: true
gamification_daily_challenges: true
gamification_experience_points: true
gamification_streaks: true
```

#### Notification Settings
```yaml
# Smart notifications
enable_advanced_notifications: true
notification_smart_timing: true
notification_personalization: true
notification_quiet_hours_start: "22:00"
notification_quiet_hours_end: "07:00"
notification_max_frequency: 5  # Per hour
notification_cooldown_minutes: 15
```

### Complete Zone Example

```yaml
zones:
  - name: "kitchen"
    icon: "mdi:chef-hat"
    purpose: "Keep the kitchen clean and organized"
    camera_entity: "camera.kitchen"
    todo_list_entity: "todo.kitchen_tasks"
    notification_service: "mobile_app_your_phone"
    notification_personality: "default"  # default, snarky, jarvis, butler, coach, zen, roaster
    update_frequency: 30  # Minutes between automatic analysis
    notifications_enabled: true
    notify_on_create: true
    notify_on_complete: true
```

## ✨ Features

### 🤖 AI-Powered Analysis

AICleaner uses advanced computer vision to:
- **Assess cleanliness** on a 0-100 scale
- **Identify specific issues** like dishes, clutter, or spills
- **Generate actionable tasks** with priority levels
- **Learn your preferences** over time

### 📱 Mobile Experience

- **Progressive Web App (PWA)** - Install as a native mobile app
- **Offline Mode** - Core features work without internet
- **Push Notifications** - Smart alerts delivered at optimal times
- **Touch Controls** - Swipe gestures and quick actions
- **Responsive Design** - Optimized for all screen sizes

### 🎮 Gamification System

- **Achievements** - Unlock 12+ badges for cleaning milestones
- **Daily Challenges** - Personalized tasks with point rewards
- **Experience Points** - Level up your cleaning game
- **Streaks** - Maintain cleaning streaks for bonus points
- **Progress Tracking** - Visual indicators and statistics

### 🔔 Smart Notifications

- **Intelligent Timing** - Delivered when you're most likely to act
- **Multiple Personalities** - Choose from 7 notification styles
- **Multi-Channel** - Home Assistant, mobile, email, webhook
- **Quiet Hours** - Respects your schedule and preferences
- **Frequency Control** - Prevents notification overload

## 🛠️ Troubleshooting

### Common Issues

#### Add-on Won't Start
**Symptoms:** Add-on fails to start or crashes immediately
**Solutions:**
1. Check that you have a valid Gemini API key
2. Verify camera entities exist and are accessible
3. Ensure todo list entities are properly configured
4. Check the add-on logs for specific error messages

#### Camera Analysis Fails
**Symptoms:** "Failed to capture camera snapshot" errors
**Solutions:**
1. Verify camera entity is working in Home Assistant
2. Check camera permissions and network connectivity
3. Ensure camera supports snapshot functionality
4. Try a different camera entity for testing

#### Notifications Not Working
**Symptoms:** No notifications received despite configuration
**Solutions:**
1. Verify notification service exists in Home Assistant
2. Test notification service manually from Developer Tools
3. Check notification settings and quiet hours configuration
4. Ensure mobile app is properly configured for push notifications

#### Poor AI Analysis Results
**Symptoms:** Inaccurate cleanliness assessments or irrelevant tasks
**Solutions:**
1. Ensure good lighting in camera view
2. Check camera angle and field of view
3. Verify camera resolution is adequate (720p minimum recommended)
4. Consider adjusting zone purpose description for better context

### Performance Optimization

#### Memory Usage
- **Monitor RAM usage** in the add-on info panel
- **Restart periodically** if memory usage grows over time
- **Reduce update frequency** for zones if experiencing issues
- **Disable unused features** like gamification if not needed

#### Network Performance
- **Use local cameras** when possible for faster analysis
- **Enable caching** to reduce API calls (enabled by default)
- **Monitor API usage** to stay within rate limits
- **Consider multi-model AI** only if you have sufficient API quotas

### Debug Mode

Enable debug logging by adding to your configuration:
```yaml
debug_mode: true
log_level: "DEBUG"
```

This provides detailed logs for troubleshooting complex issues.

## 📡 API Reference

### Service Calls

#### `aicleaner.run_analysis`
Trigger immediate analysis of one or all zones.

**Parameters:**
- `zone_name` (optional): Specific zone to analyze
- `force_refresh` (optional): Bypass cache for fresh analysis

**Example:**
```yaml
service: aicleaner.run_analysis
data:
  zone_name: "kitchen"
  force_refresh: true
```

#### `aicleaner.run_optimized_analysis`
Run performance-optimized analysis with intelligent caching.

**Parameters:**
- `zone_name` (optional): Specific zone to analyze
- `batch_mode` (optional): Process multiple zones efficiently

#### `aicleaner.complete_task`
Mark a specific task as completed.

**Parameters:**
- `zone_name`: Zone containing the task
- `task_id`: Unique task identifier

#### `aicleaner.get_zone_status`
Retrieve current status and metrics for a zone.

**Parameters:**
- `zone_name`: Zone to query

**Returns:**
- Cleanliness score
- Active tasks count
- Last analysis timestamp
- Performance metrics

### Sensor Entities

AICleaner creates the following sensor entities:

- `sensor.aicleaner_[zone]_cleanliness` - Cleanliness score (0-100)
- `sensor.aicleaner_[zone]_tasks_active` - Number of active tasks
- `sensor.aicleaner_[zone]_tasks_completed` - Total completed tasks
- `sensor.aicleaner_[zone]_last_analysis` - Timestamp of last analysis
- `sensor.aicleaner_system_status` - Overall system health

### Events

AICleaner fires these events for automation:

- `aicleaner_analysis_complete` - Analysis finished for a zone
- `aicleaner_task_created` - New cleaning task generated
- `aicleaner_task_completed` - Task marked as complete
- `aicleaner_achievement_unlocked` - New achievement earned
- `aicleaner_daily_challenge_complete` - Daily challenge finished

## 🔧 Advanced Usage

### Automation Examples

#### Trigger Robot Vacuum on Low Cleanliness
```yaml
automation:
  - alias: "Auto Vacuum on Messy Kitchen"
    trigger:
      - platform: numeric_state
        entity_id: sensor.aicleaner_kitchen_cleanliness
        below: 30
    action:
      - service: vacuum.start
        target:
          entity_id: vacuum.kitchen_robot
```

#### Send Weekly Cleaning Summary
```yaml
automation:
  - alias: "Weekly Cleaning Report"
    trigger:
      - platform: time
        at: "19:00:00"
    condition:
      - condition: time
        weekday:
          - sun
    action:
      - service: notify.mobile_app_your_phone
        data:
          title: "Weekly Cleaning Summary"
          message: >
            Kitchen: {{ states('sensor.aicleaner_kitchen_cleanliness') }}%
            Tasks completed: {{ states('sensor.aicleaner_kitchen_tasks_completed') }}
```

### Custom Integrations

#### Webhook Notifications
Configure webhook notifications for external systems:
```yaml
notification_channels:
  - "webhook"
webhook_url: "https://your-webhook-endpoint.com/aicleaner"
webhook_headers:
  Authorization: "Bearer your-token"
```

#### Voice Assistant Integration
Use with Google Assistant or Alexa:
```yaml
# Google Assistant
"Hey Google, ask Home Assistant about kitchen cleanliness"

# Alexa
"Alexa, ask Home Assistant to run kitchen analysis"
```

---

For additional support, visit our [GitHub repository](https://github.com/sporebattyl/Aiclean) or join the [community discussions](https://github.com/sporebattyl/Aiclean/discussions).
