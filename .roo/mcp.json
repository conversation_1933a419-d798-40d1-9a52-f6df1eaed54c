{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/root/addons/Aiclean"], "env": {}}, "brave-search": {"command": "npx", "args": ["@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": ""}}, "puppeteer": {"command": "npx", "args": ["@modelcontextprotocol/server-puppeteer"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"], "env": {}}}}