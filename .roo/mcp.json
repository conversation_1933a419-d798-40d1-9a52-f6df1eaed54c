{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/root/addons/Aiclean"], "env": {}}, "brave-search": {"command": "npx", "args": ["@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSA0Iv5TiOTlCHrCSha2hkoo6PkiA7o"}}, "puppeteer": {"command": "npx", "args": ["@modelcontextprotocol/server-puppeteer"], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "notion-mcp-server": {"command": "npx", "args": ["notion-mcp-server"], "env": {"NOTION_TOKEN": "ntn_b94830069485lpggmhgzowoeU0wyoSq8mTayfEQEnk56tx", "NOTION_PAGE_ID": "2202353b-33e4-8014-9b1f-d31d4cbb309d"}}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "env": {}}, "everything": {"command": "npx", "args": ["@modelcontextprotocol/server-everything"], "env": {}}}}