#!/usr/bin/env python3
"""
Simple Notion API Integration for Augment Agent
Provides reliable Notion workspace updates
"""

import os
import sys
import json
import requests
from typing import Dict, List, Any

def load_env_vars():
    """Load environment variables from config files"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"\''')

def append_to_notion(title: str, items: List[str], summary: str = None, emoji: str = "🎯") -> bool:
    """Append a progress update to Notion workspace"""
    
    # Load environment
    load_env_vars()
    
    token = os.getenv('NOTION_TOKEN')
    page_id = os.getenv('NOTION_PAGE_ID')
    
    if not token or not page_id:
        print("❌ Missing NOTION_TOKEN or NOTION_PAGE_ID")
        return False
    
    # Create blocks
    blocks = []
    
    # Add callout
    blocks.append({
        "object": "block",
        "type": "callout",
        "callout": {
            "rich_text": [{"type": "text", "text": {"content": title}}],
            "icon": {"type": "emoji", "emoji": emoji},
            "color": "default"
        }
    })
    
    # Add heading
    blocks.append({
        "object": "block",
        "type": "heading_2",
        "heading_2": {
            "rich_text": [{"type": "text", "text": {"content": "Progress Update"}}]
        }
    })
    
    # Add bullet points
    for item in items:
        blocks.append({
            "object": "block",
            "type": "bulleted_list_item",
            "bulleted_list_item": {
                "rich_text": [{"type": "text", "text": {"content": item}}]
            }
        })
    
    # Add summary if provided
    if summary:
        blocks.append({
            "object": "block",
            "type": "paragraph",
            "paragraph": {
                "rich_text": [{"type": "text", "text": {"content": summary}}]
            }
        })
    
    # Send to Notion
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "Notion-Version": "2022-06-28"
    }
    payload = {"children": blocks}
    
    try:
        response = requests.patch(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        print(f"✅ Successfully added progress update to Notion: {title}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ Error updating Notion: {e}")
        return False

def main():
    """Command line interface"""
    if len(sys.argv) < 3:
        print("Usage: python notion_update.py <title> <item1,item2,...> [summary]")
        print("Example: python notion_update.py 'Test Complete' 'All tests passing,No errors found' 'Ready for deployment'")
        sys.exit(1)
    
    title = sys.argv[1]
    items = sys.argv[2].split(',')
    summary = sys.argv[3] if len(sys.argv) > 3 else None
    
    success = append_to_notion(title, items, summary)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
