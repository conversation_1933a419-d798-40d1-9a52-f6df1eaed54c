#!/usr/bin/env python3
"""
Test Notion MCP Server Integration
Tests the Notion MCP server and provides a bridge for Augment Agent
"""

import os
import sys
import json
import subprocess
import time
import signal
from typing import Dict, Any, Optional

class NotionMCPBridge:
    """Bridge between Augment Agent and Notion MCP Server"""
    
    def __init__(self):
        self.server_process = None
        self.notion_token = os.getenv('NOTION_TOKEN')
        self.notion_page_id = os.getenv('NOTION_PAGE_ID')
        
        if not self.notion_token:
            raise ValueError("NOTION_TOKEN environment variable not set")
        if not self.notion_page_id:
            raise ValueError("NOTION_PAGE_ID environment variable not set")
    
    def start_server(self) -> bool:
        """Start the Notion MCP server"""
        try:
            env = os.environ.copy()
            env['NOTION_TOKEN'] = self.notion_token
            env['NOTION_PAGE_ID'] = self.notion_page_id
            
            # Start the MCP server
            self.server_process = subprocess.Popen(
                ['notion-mcp-server'],
                env=env,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give it a moment to start
            time.sleep(2)
            
            # Check if it's still running
            if self.server_process.poll() is None:
                print("✅ Notion MCP server started successfully")
                return True
            else:
                print("❌ Notion MCP server failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error starting Notion MCP server: {e}")
            return False
    
    def stop_server(self):
        """Stop the Notion MCP server"""
        if self.server_process:
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            print("🛑 Notion MCP server stopped")
    
    def send_mcp_request(self, method: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Send a request to the MCP server"""
        if not self.server_process or self.server_process.poll() is not None:
            print("❌ MCP server is not running")
            return None
        
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }
        
        try:
            # Send request
            request_json = json.dumps(request) + '\n'
            self.server_process.stdin.write(request_json)
            self.server_process.stdin.flush()
            
            # Read response
            response_line = self.server_process.stdout.readline()
            if response_line:
                return json.loads(response_line.strip())
            else:
                print("❌ No response from MCP server")
                return None
                
        except Exception as e:
            print(f"❌ Error communicating with MCP server: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Test the MCP server connection"""
        print("🧪 Testing Notion MCP server connection...")
        
        # Test initialize
        response = self.send_mcp_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "augment-agent-test",
                "version": "1.0.0"
            }
        })
        
        if response and "result" in response:
            print("✅ MCP server initialization successful")
            
            # Test list tools
            tools_response = self.send_mcp_request("tools/list")
            if tools_response and "result" in tools_response:
                tools = tools_response["result"]["tools"]
                print(f"✅ Found {len(tools)} available tools:")
                for tool in tools:
                    print(f"   - {tool['name']}: {tool.get('description', 'No description')}")
                return True
            else:
                print("❌ Failed to list tools")
                return False
        else:
            print("❌ MCP server initialization failed")
            return False
    
    def append_blocks_to_notion(self, blocks: list) -> bool:
        """Append blocks to the configured Notion page"""
        print(f"📝 Appending {len(blocks)} blocks to Notion page...")
        
        response = self.send_mcp_request("tools/call", {
            "name": "notion_blocks",
            "arguments": {
                "payload": {
                    "action": "append_block_children",
                    "params": {
                        "block_id": self.notion_page_id,
                        "children": blocks
                    }
                }
            }
        })
        
        if response and "result" in response:
            print("✅ Successfully appended blocks to Notion")
            return True
        else:
            print("❌ Failed to append blocks to Notion")
            if response and "error" in response:
                print(f"   Error: {response['error']}")
            return False

def main():
    """Test the Notion MCP integration"""
    print("🚀 Testing Notion MCP Server Integration")
    print("=" * 50)
    
    # Load environment variables from both .env.secrets and .env.mcp
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            print(f"📁 Loading environment variables from {env_file}...")
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"\'')
    
    try:
        # Create bridge
        bridge = NotionMCPBridge()
        
        # Start server
        if not bridge.start_server():
            sys.exit(1)
        
        # Test connection
        if not bridge.test_connection():
            bridge.stop_server()
            sys.exit(1)
        
        # Test adding content
        test_blocks = [
            {
                "object": "block",
                "type": "callout",
                "callout": {
                    "rich_text": [{"type": "text", "text": {"content": "🧪 MCP Integration Test - Connection Successful!"}}],
                    "icon": {"type": "emoji", "emoji": "✅"},
                    "color": "default"
                }
            },
            {
                "object": "block",
                "type": "paragraph",
                "paragraph": {
                    "rich_text": [{"type": "text", "text": {"content": f"Test completed at {time.strftime('%Y-%m-%d %H:%M:%S')}"}}]
                }
            }
        ]
        
        if bridge.append_blocks_to_notion(test_blocks):
            print("\n🎉 Notion MCP integration test completed successfully!")
            print("   The MCP server is working and can update your Notion workspace.")
        else:
            print("\n❌ Notion MCP integration test failed")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)
    
    finally:
        if 'bridge' in locals():
            bridge.stop_server()

if __name__ == "__main__":
    main()
