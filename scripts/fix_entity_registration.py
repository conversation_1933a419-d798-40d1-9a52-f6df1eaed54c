#!/usr/bin/env python3
"""
AICleaner Entity Registration Fix Script

This script fixes the issue where AICleaner Lovelace card loads but shows "No zones configured"
by converting the addon from using /api/states (temporary entities) to proper template sensors.

The fix involves:
1. Adding template trigger sensors to configuration.yaml
2. These sensors create real, persistent Home Assistant entities
3. The Lovelace card can then access the real entities

Usage:
    python3 scripts/fix_entity_registration.py
"""

import os
import sys
import yaml
import json
import shutil
from pathlib import Path
from datetime import datetime

class EntityRegistrationFixer:
    def __init__(self):
        self.config_path = Path("/config")
        self.config_yaml_path = self.config_path / "configuration.yaml"
        self.backup_path = self.config_path / f"configuration.yaml.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def backup_configuration(self):
        """Create backup of current configuration.yaml"""
        try:
            if self.config_yaml_path.exists():
                shutil.copy2(self.config_yaml_path, self.backup_path)
                print(f"✅ Backup created: {self.backup_path}")
                return True
            else:
                print(f"❌ Configuration file not found: {self.config_yaml_path}")
                return False
        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            return False
    
    def load_current_config(self):
        """Load current configuration.yaml"""
        try:
            if self.config_yaml_path.exists():
                with open(self.config_yaml_path, 'r') as f:
                    return yaml.safe_load(f) or {}
            else:
                return {}
        except Exception as e:
            print(f"❌ Error loading configuration: {e}")
            return {}
    
    def get_aicleaner_zones(self):
        """Get configured zones from AICleaner addon"""
        try:
            # Try to get zones from addon options
            options_path = Path("/data/options.json")
            if options_path.exists():
                with open(options_path, 'r') as f:
                    options = json.load(f)
                    zones = options.get('zones', [])
                    if zones:
                        return [zone.get('name', '').lower().replace(' ', '_') for zone in zones]
            
            # Fallback: assume kitchen zone exists (from logs)
            return ['kitchen']
            
        except Exception as e:
            print(f"⚠️  Could not determine zones, using default: {e}")
            return ['kitchen']
    
    def generate_template_sensors(self, zones):
        """Generate template sensor configuration for AICleaner zones"""
        template_config = []
        
        # Global system status sensor
        system_sensor = {
            'trigger': {
                'platform': 'state',
                'entity_id': 'sensor.aicleaner_system_status'
            },
            'sensor': [{
                'name': 'AICleaner System Status',
                'unique_id': 'aicleaner_system_status_persistent',
                'state': "{{ trigger.to_state.state if trigger.to_state else 'inactive' }}",
                'icon': 'mdi:robot-vacuum',
                'attributes': {
                    'total_zones': "{{ trigger.to_state.attributes.total_zones if trigger.to_state else 0 }}",
                    'active_tasks': "{{ trigger.to_state.attributes.active_tasks if trigger.to_state else 0 }}",
                    'completed_tasks': "{{ trigger.to_state.attributes.completed_tasks if trigger.to_state else 0 }}",
                    'completion_rate': "{{ trigger.to_state.attributes.completion_rate if trigger.to_state else 0 }}",
                    'last_analysis': "{{ trigger.to_state.attributes.last_analysis if trigger.to_state else 'never' }}",
                    'version': "{{ trigger.to_state.attributes.version if trigger.to_state else '2.0' }}",
                    'zones': "{{ trigger.to_state.attributes.zones if trigger.to_state else [] }}"
                }
            }]
        }
        template_config.append(system_sensor)
        
        # Zone-specific sensors
        zone_icons = {
            'kitchen': 'mdi:chef-hat',
            'living_room': 'mdi:sofa',
            'bedroom': 'mdi:bed',
            'bathroom': 'mdi:shower',
            'office': 'mdi:desk',
            'dining_room': 'mdi:table-chair'
        }
        
        for zone in zones:
            zone_sensor = {
                'trigger': {
                    'platform': 'state',
                    'entity_id': f'sensor.aicleaner_{zone}_tasks'
                },
                'sensor': [{
                    'name': f'AICleaner {zone.replace("_", " ").title()} Tasks',
                    'unique_id': f'aicleaner_{zone}_tasks_persistent',
                    'state': "{{ trigger.to_state.state if trigger.to_state else 'unknown' }}",
                    'icon': zone_icons.get(zone, 'mdi:home'),
                    'attributes': {
                        'zone_name': f"{{{{ trigger.to_state.attributes.zone_name if trigger.to_state else '{zone}' }}}}",
                        'active_tasks': "{{ trigger.to_state.attributes.active_tasks if trigger.to_state else [] }}",
                        'completed_tasks': "{{ trigger.to_state.attributes.completed_tasks if trigger.to_state else [] }}",
                        'completion_rate': "{{ trigger.to_state.attributes.completion_rate if trigger.to_state else 0 }}",
                        'last_analysis': "{{ trigger.to_state.attributes.last_analysis if trigger.to_state else 'never' }}",
                        'cleanliness_score': "{{ trigger.to_state.attributes.cleanliness_score if trigger.to_state else 0 }}",
                        'cleanliness_state': "{{ trigger.to_state.attributes.cleanliness_state if trigger.to_state else 'unknown' }}",
                        'analysis_count': "{{ trigger.to_state.attributes.analysis_count if trigger.to_state else 0 }}"
                    }
                }]
            }
            template_config.append(zone_sensor)
        
        return template_config
    
    def update_configuration(self, zones):
        """Update configuration.yaml with template sensors"""
        try:
            # Load current config
            config = self.load_current_config()
            
            # Generate template sensors
            template_sensors = self.generate_template_sensors(zones)
            
            # Add or update template section
            if 'template' not in config:
                config['template'] = []
            elif not isinstance(config['template'], list):
                config['template'] = [config['template']]
            
            # Add AICleaner template sensors
            config['template'].extend(template_sensors)
            
            # Write updated configuration
            with open(self.config_yaml_path, 'w') as f:
                yaml.dump(config, f, default_flow_style=False, sort_keys=False)
            
            print(f"✅ Configuration updated with {len(template_sensors)} template sensors")
            return True
            
        except Exception as e:
            print(f"❌ Error updating configuration: {e}")
            return False
    
    def run_fix(self):
        """Run the complete entity registration fix"""
        print("🔧 AICleaner Entity Registration Fix")
        print("=" * 50)
        
        # Step 1: Backup configuration
        print("1. Creating configuration backup...")
        if not self.backup_configuration():
            return False
        
        # Step 2: Get zones
        print("2. Detecting AICleaner zones...")
        zones = self.get_aicleaner_zones()
        print(f"   Found zones: {', '.join(zones)}")
        
        # Step 3: Update configuration
        print("3. Adding template sensors to configuration.yaml...")
        if not self.update_configuration(zones):
            return False
        
        # Step 4: Instructions
        print("\n✅ Fix applied successfully!")
        print("\n📋 Next Steps:")
        print("1. Restart Home Assistant")
        print("2. Check Settings > Devices & Services > Entities")
        print("3. Search for 'aicleaner' - you should see persistent entities")
        print("4. Refresh your Lovelace dashboard")
        print("5. The AICleaner card should now show your zones")
        
        print(f"\n💾 Backup saved to: {self.backup_path}")
        print("   (Restore with: cp {backup_path} /config/configuration.yaml)")
        
        return True

if __name__ == "__main__":
    fixer = EntityRegistrationFixer()
    success = fixer.run_fix()
    sys.exit(0 if success else 1)
