#!/usr/bin/env python3
"""
Test script for Mobile Integration system in AICleaner
Tests mobile configuration, quick actions, and responsive features
"""

import os
import sys
import json
import tempfile

# Add the aicleaner directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'aicleaner'))

def test_mobile_integration_system():
    """Test the mobile integration system"""
    print("📱 AICleaner Mobile Integration Test")
    print("=" * 45)
    
    try:
        from mobile_integration import MobileIntegration, MobileConfig, QuickAction, MobileFeature
        print("✅ Mobile integration modules imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import mobile integration modules: {e}")
        return False
    
    # Test 1: System initialization
    print("\n🚀 Test 1: System Initialization")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            mobile_system = MobileIntegration(config_path=temp_dir)
            print("✅ Mobile integration system initialized")
            print(f"   Config path: {temp_dir}")
        except Exception as e:
            print(f"❌ Failed to initialize mobile integration: {e}")
            return False
        
        # Test 2: Mobile configuration
        print("\n⚙️ Test 2: Mobile Configuration")
        
        try:
            config = mobile_system.get_mobile_config()
            print("✅ Mobile configuration retrieved")
            print(f"   Push notifications: {config['enable_push_notifications']}")
            print(f"   Quick actions: {config['enable_quick_actions']}")
            print(f"   Gesture controls: {config['enable_gesture_controls']}")
            print(f"   Theme preference: {config['theme_preference']}")
        except Exception as e:
            print(f"❌ Mobile configuration test failed: {e}")
            return False
        
        # Test 3: Configuration updates
        print("\n🔧 Test 3: Configuration Updates")
        
        try:
            updates = {
                'enable_push_notifications': False,
                'theme_preference': 'dark',
                'compact_mode': True
            }
            
            success = mobile_system.update_mobile_config(updates)
            if success:
                updated_config = mobile_system.get_mobile_config()
                print("✅ Configuration updated successfully")
                print(f"   Push notifications: {updated_config['enable_push_notifications']}")
                print(f"   Theme preference: {updated_config['theme_preference']}")
                print(f"   Compact mode: {updated_config['compact_mode']}")
            else:
                print("❌ Configuration update failed")
                return False
        except Exception as e:
            print(f"❌ Configuration update test failed: {e}")
            return False
        
        # Test 4: Quick actions
        print("\n⚡ Test 4: Quick Actions")
        
        try:
            quick_actions = mobile_system.get_quick_actions()
            print(f"✅ Retrieved {len(quick_actions)} default quick actions")
            
            for i, action in enumerate(quick_actions[:3], 1):
                print(f"   {i}. {action['title']} ({action['icon']})")
                print(f"      Action: {action['action_type']} -> {action['target']}")
        except Exception as e:
            print(f"❌ Quick actions test failed: {e}")
            return False
        
        # Test 5: Add custom quick action
        print("\n➕ Test 5: Add Custom Quick Action")
        
        try:
            custom_action = {
                'id': 'test_action',
                'title': 'Test Action',
                'icon': 'mdi:test-tube',
                'action_type': 'service_call',
                'target': 'test.service',
                'parameters': {'test': True}
            }
            
            success = mobile_system.add_quick_action(custom_action)
            if success:
                updated_actions = mobile_system.get_quick_actions()
                print("✅ Custom quick action added successfully")
                print(f"   Total actions: {len(updated_actions)}")
                
                # Find the added action
                test_action = next((a for a in updated_actions if a['id'] == 'test_action'), None)
                if test_action:
                    print(f"   Added: {test_action['title']}")
            else:
                print("❌ Failed to add custom quick action")
                return False
        except Exception as e:
            print(f"❌ Add quick action test failed: {e}")
            return False
        
        # Test 6: Remove quick action
        print("\n➖ Test 6: Remove Quick Action")
        
        try:
            success = mobile_system.remove_quick_action('test_action')
            if success:
                updated_actions = mobile_system.get_quick_actions()
                print("✅ Quick action removed successfully")
                print(f"   Remaining actions: {len(updated_actions)}")
                
                # Verify removal
                test_action = next((a for a in updated_actions if a['id'] == 'test_action'), None)
                if not test_action:
                    print("   ✅ Action successfully removed")
                else:
                    print("   ⚠️ Action still present after removal")
            else:
                print("❌ Failed to remove quick action")
                return False
        except Exception as e:
            print(f"❌ Remove quick action test failed: {e}")
            return False
        
        # Test 7: Mobile layouts
        print("\n📐 Test 7: Mobile Layouts")
        
        try:
            screen_sizes = ['mobile', 'tablet', 'desktop']
            for screen_size in screen_sizes:
                layout = mobile_system.get_mobile_optimized_layout(screen_size)
                print(f"   {screen_size.title()} layout:")
                print(f"     Grid columns: {layout['grid_columns']}")
                print(f"     Button height: {layout['button_height']}")
                print(f"     Compact mode: {layout['compact_mode']}")
                print(f"     Max items: {layout['max_items_per_view']}")
            
            print("✅ Mobile layouts generated successfully")
        except Exception as e:
            print(f"❌ Mobile layouts test failed: {e}")
            return False
        
        # Test 8: PWA manifest
        print("\n📲 Test 8: PWA Manifest Generation")
        
        try:
            manifest = mobile_system.generate_mobile_manifest()
            print("✅ PWA manifest generated")
            print(f"   App name: {manifest['name']}")
            print(f"   Short name: {manifest['short_name']}")
            print(f"   Display mode: {manifest['display']}")
            print(f"   Theme color: {manifest['theme_color']}")
            print(f"   Icons: {len(manifest['icons'])}")
            print(f"   Categories: {', '.join(manifest['categories'])}")
        except Exception as e:
            print(f"❌ PWA manifest test failed: {e}")
            return False
        
        # Test 9: Notification configuration
        print("\n🔔 Test 9: Notification Configuration")
        
        try:
            notification_config = mobile_system.get_notification_config()
            print("✅ Notification configuration retrieved")
            print(f"   Enabled: {notification_config['enabled']}")
            print(f"   Sound: {notification_config['sound']}")
            print(f"   Vibration: {notification_config['vibration']}")
            print(f"   Badge: {notification_config['badge']}")
            print(f"   Actions: {len(notification_config['actions'])}")
        except Exception as e:
            print(f"❌ Notification configuration test failed: {e}")
            return False
        
        # Test 10: System status
        print("\n📊 Test 10: System Status")
        
        try:
            status = mobile_system.get_system_status()
            print("✅ System status retrieved")
            print(f"   Config loaded: {status['mobile_config_loaded']}")
            print(f"   Quick actions: {status['quick_actions_count']}")
            print(f"   Enabled features: {len(status['enabled_features'])}")
            if status['enabled_features']:
                print(f"     Features: {', '.join(status['enabled_features'])}")
            print(f"   Config path: {status['config_path']}")
        except Exception as e:
            print(f"❌ System status test failed: {e}")
            return False
        
        # Test 11: Data persistence
        print("\n💾 Test 11: Data Persistence")
        
        try:
            # Create new instance to test data loading
            mobile_system2 = MobileIntegration(config_path=temp_dir)
            config2 = mobile_system2.get_mobile_config()
            actions2 = mobile_system2.get_quick_actions()
            
            # Verify data was persisted
            if (config2['theme_preference'] == 'dark' and 
                config2['compact_mode'] == True and
                len(actions2) > 0):
                print("✅ Data persistence working correctly")
                print(f"   Config preserved: theme={config2['theme_preference']}, compact={config2['compact_mode']}")
                print(f"   Actions preserved: {len(actions2)} actions")
            else:
                print("⚠️ Data persistence issue detected")
        except Exception as e:
            print(f"❌ Data persistence test failed: {e}")
            return False
    
    print("\n🎉 Mobile Integration Test Completed Successfully!")
    return True


def test_aicleaner_integration():
    """Test AICleaner integration with mobile features"""
    print("\n🏠 AICleaner Integration Test")
    print("=" * 40)
    
    # Test configuration with mobile integration enabled
    test_config = {
        'gemini_api_key': os.getenv('GEMINI_API_KEY', 'test_key'),
        'enable_mobile_integration': True,
        'display_name': 'Test User',
        'zones': [{
            'name': 'test_kitchen',
            'icon': 'mdi:chef-hat',
            'purpose': 'cooking and food preparation',
            'camera_entity': 'camera.test',
            'todo_list_entity': 'todo.test',
            'update_frequency': 30,
            'notifications_enabled': False,
            'notification_service': '',
            'notification_personality': 'default',
            'notify_on_create': False,
            'notify_on_complete': False
        }]
    }
    
    print(f"✅ Test configuration prepared")
    print(f"   Mobile integration: {test_config['enable_mobile_integration']}")
    print(f"   Test zone: {test_config['zones'][0]['name']}")
    
    # Test mobile integration methods would be available in AICleaner
    expected_methods = [
        'get_mobile_config',
        'update_mobile_config',
        'add_mobile_quick_action',
        'remove_mobile_quick_action',
        'get_mobile_layout'
    ]
    
    print(f"✅ Expected AICleaner methods: {', '.join(expected_methods)}")
    
    return True


if __name__ == "__main__":
    print("🚀 Starting AICleaner Mobile Integration Tests")
    print("=" * 55)
    
    success = True
    
    # Run mobile integration system tests
    if not test_mobile_integration_system():
        success = False
    
    # Run AICleaner integration tests
    if not test_aicleaner_integration():
        success = False
    
    print("\n" + "=" * 55)
    if success:
        print("🎉 All tests completed successfully!")
        print("✅ Mobile integration system is ready for production use")
    else:
        print("❌ Some tests failed - please review the output above")
        sys.exit(1)
