#!/usr/bin/env python3
"""
Lovelace Card Integration Testing
Verifies the AICleaner Lovelace card loads correctly and displays real-time data
"""
import os
import sys
import time
import json
import requests
from datetime import datetime

def load_env():
    """Load environment variables"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        os.environ[key] = value.strip('"').strip("'")

class LovelaceCardTester:
    """Lovelace card integration tester"""
    
    def __init__(self):
        self.supervisor_token = os.getenv('SUPERVISOR_TOKEN')
        self.ha_endpoint = 'http://supervisor/core/api'
        
        # Card endpoints to test
        self.card_urls = [
            'http://***********:8099/aicleaner-card.js',
            'http://localhost:8099/aicleaner-card.js'
        ]
        
        self.test_results = {}
    
    def test_card_accessibility(self):
        """Test if the Lovelace card is accessible"""
        print("📡 Test 1: Card Accessibility")
        
        for url in self.card_urls:
            try:
                response = requests.get(url, timeout=10)
                
                if response.ok:
                    content_length = len(response.content)
                    content_type = response.headers.get('Content-Type', 'unknown')
                    
                    print(f"   ✅ {url}")
                    print(f"      Size: {content_length:,} bytes")
                    print(f"      Type: {content_type}")
                    
                    # Analyze content
                    content = response.text
                    
                    # Check for key card components
                    has_class_definition = 'class' in content and 'LitElement' in content
                    has_render_method = 'render()' in content or 'render (' in content
                    has_aicleaner_refs = 'aicleaner' in content.lower()
                    has_ha_integration = 'hass' in content.lower()
                    
                    print(f"      ✅ Class definition: {'Yes' if has_class_definition else 'No'}")
                    print(f"      ✅ Render method: {'Yes' if has_render_method else 'No'}")
                    print(f"      ✅ AICleaner refs: {'Yes' if has_aicleaner_refs else 'No'}")
                    print(f"      ✅ HA integration: {'Yes' if has_ha_integration else 'No'}")
                    
                    self.test_results['card_accessibility'] = {
                        'success': True,
                        'url': url,
                        'size': content_length,
                        'content_type': content_type,
                        'has_class_definition': has_class_definition,
                        'has_render_method': has_render_method,
                        'has_aicleaner_refs': has_aicleaner_refs,
                        'has_ha_integration': has_ha_integration
                    }
                    return True
                else:
                    print(f"   ❌ {url}: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {url}: {str(e)[:50]}...")
        
        self.test_results['card_accessibility'] = {'success': False}
        return False
    
    def test_sensor_data_availability(self):
        """Test if AICleaner sensor data is available in HA"""
        print("\n📊 Test 2: Sensor Data Availability")
        
        try:
            # Look for AICleaner sensors
            states_url = f"{self.ha_endpoint}/states"
            response = requests.get(
                states_url,
                headers={'Authorization': f'Bearer {self.supervisor_token}'},
                timeout=10
            )
            
            if response.ok:
                states = response.json()
                
                # Find AICleaner related entities
                aicleaner_entities = []
                for state in states:
                    entity_id = state.get('entity_id', '')
                    if 'aicleaner' in entity_id.lower() or 'cleaning' in entity_id.lower():
                        aicleaner_entities.append({
                            'entity_id': entity_id,
                            'state': state.get('state'),
                            'last_updated': state.get('last_updated'),
                            'attributes': state.get('attributes', {})
                        })
                
                print(f"   ✅ Found {len(aicleaner_entities)} AICleaner-related entities")
                
                for entity in aicleaner_entities[:5]:  # Show first 5
                    print(f"      • {entity['entity_id']}: {entity['state']}")
                
                # Check for specific expected entities
                expected_patterns = ['sensor.aicleaner', 'todo.', 'camera.rowan_room']
                found_patterns = []
                
                for pattern in expected_patterns:
                    found = any(pattern in entity['entity_id'] for entity in aicleaner_entities)
                    found_patterns.append(found)
                    status = "✅" if found else "⚠️"
                    print(f"      {status} {pattern}: {'Found' if found else 'Not found'}")
                
                self.test_results['sensor_data'] = {
                    'success': True,
                    'entities_found': len(aicleaner_entities),
                    'entities': aicleaner_entities,
                    'expected_patterns_found': sum(found_patterns)
                }
                return True
            else:
                print(f"   ❌ Failed to get states: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        self.test_results['sensor_data'] = {'success': False}
        return False
    
    def test_card_configuration(self):
        """Test card configuration and integration points"""
        print("\n⚙️  Test 3: Card Configuration")
        
        try:
            # Get the card content to analyze configuration
            card_url = self.card_urls[0]  # Use first working URL
            response = requests.get(card_url, timeout=10)
            
            if response.ok:
                content = response.text
                
                # Check for configuration options
                config_checks = {
                    'entity_config': 'entity' in content and 'config' in content,
                    'zone_support': 'zone' in content.lower(),
                    'task_management': 'task' in content.lower(),
                    'analytics_support': 'analytics' in content.lower() or 'chart' in content.lower(),
                    'notification_support': 'notification' in content.lower() or 'alert' in content.lower(),
                    'theme_support': 'theme' in content.lower() or 'style' in content.lower()
                }
                
                print("   Configuration Features:")
                for feature, found in config_checks.items():
                    status = "✅" if found else "⚠️"
                    print(f"      {status} {feature.replace('_', ' ').title()}: {'Yes' if found else 'No'}")
                
                # Check for error handling
                error_handling = 'error' in content.lower() and 'catch' in content.lower()
                print(f"      ✅ Error handling: {'Yes' if error_handling else 'No'}")
                
                self.test_results['card_configuration'] = {
                    'success': True,
                    'features': config_checks,
                    'error_handling': error_handling
                }
                return True
            else:
                print(f"   ❌ Failed to get card content")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        self.test_results['card_configuration'] = {'success': False}
        return False
    
    def test_real_time_data_flow(self):
        """Test if the card can receive real-time data updates"""
        print("\n🔄 Test 4: Real-time Data Flow")
        
        try:
            # Create a test sensor update to see if card would receive it
            # This simulates what the card would receive from HA
            
            # Get current todo list state
            todo_entity = 'todo.rowan_room_cleaning_to_do'
            state_url = f"{self.ha_endpoint}/states/{todo_entity}"
            
            response = requests.get(
                state_url,
                headers={'Authorization': f'Bearer {self.supervisor_token}'},
                timeout=10
            )
            
            if response.ok:
                todo_state = response.json()
                current_items = len(todo_state.get('attributes', {}).get('items', []))
                
                print(f"   ✅ Current todo items: {current_items}")
                print(f"   ✅ Todo state: {todo_state.get('state')}")
                print(f"   ✅ Last updated: {todo_state.get('last_updated')}")
                
                # Add a test item to trigger state change
                add_url = f"{self.ha_endpoint}/services/todo/add_item"
                test_item = f"Lovelace Card Test - {datetime.now().strftime('%H:%M:%S')}"
                
                add_response = requests.post(
                    add_url,
                    headers={'Authorization': f'Bearer {self.supervisor_token}'},
                    json={
                        'entity_id': todo_entity,
                        'item': test_item
                    },
                    timeout=10
                )
                
                if add_response.ok:
                    print(f"   ✅ Test item added: {test_item}")
                    
                    # Wait a moment and check updated state
                    time.sleep(2)
                    
                    updated_response = requests.get(
                        state_url,
                        headers={'Authorization': f'Bearer {self.supervisor_token}'},
                        timeout=10
                    )
                    
                    if updated_response.ok:
                        updated_state = updated_response.json()
                        updated_items = len(updated_state.get('attributes', {}).get('items', []))
                        
                        if updated_items > current_items:
                            print(f"   ✅ State updated: {updated_items} items (was {current_items})")
                            print(f"   ✅ Real-time data flow working")
                        else:
                            print(f"   ⚠️  State may not have updated yet")
                    
                    self.test_results['real_time_data'] = {
                        'success': True,
                        'initial_items': current_items,
                        'test_item_added': True,
                        'state_updated': updated_items > current_items if 'updated_items' in locals() else False
                    }
                    return True
                else:
                    print(f"   ❌ Failed to add test item")
            else:
                print(f"   ❌ Failed to get todo state")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        self.test_results['real_time_data'] = {'success': False}
        return False
    
    def run_integration_tests(self):
        """Run all Lovelace card integration tests"""
        print("🃏 Lovelace Card Integration Testing")
        print("=" * 60)
        
        # Run tests
        test_1 = self.test_card_accessibility()
        test_2 = self.test_sensor_data_availability()
        test_3 = self.test_card_configuration()
        test_4 = self.test_real_time_data_flow()
        
        # Summary
        tests = [test_1, test_2, test_3, test_4]
        test_names = ['Card Accessibility', 'Sensor Data', 'Configuration', 'Real-time Data']
        
        print("\n📊 Test Results Summary")
        print("=" * 60)
        
        passed_tests = sum(tests)
        for i, (test_name, passed) in enumerate(zip(test_names, tests)):
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{test_name}: {status}")
        
        print(f"\nOverall: {passed_tests}/{len(tests)} tests passed")
        print(f"Success rate: {(passed_tests/len(tests))*100:.1f}%")
        
        # Save results
        final_results = {
            'timestamp': datetime.now().isoformat(),
            'tests_passed': passed_tests,
            'tests_total': len(tests),
            'success_rate': (passed_tests/len(tests))*100,
            'test_details': self.test_results
        }
        
        with open('/tmp/lovelace_card_test_results.json', 'w') as f:
            json.dump(final_results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: /tmp/lovelace_card_test_results.json")
        
        return passed_tests == len(tests)

def main():
    """Main test function"""
    load_env()
    
    tester = LovelaceCardTester()
    success = tester.run_integration_tests()
    
    if success:
        print("\n🎉 Lovelace Card Integration: PASSED")
        print("   Card is properly integrated and ready for use!")
    else:
        print("\n⚠️  Lovelace Card Integration: PARTIAL")
        print("   Some tests failed - check results for details")
    
    return success

if __name__ == "__main__":
    main()
