#!/usr/bin/env python3
"""
Add structured content and databases to the reorganized Notion workspace
"""

import os
import requests
import json
from datetime import datetime, timezone

class NotionContentBuilder:
    def __init__(self):
        self.token = os.getenv('NOTION_TOKEN')
        self.page_id = '2202353b-33e4-8014-9b1f-d31d4cbb309d'
        self.headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json',
            'Notion-Version': '2022-06-28'
        }
    
    def create_phase4_task_database(self):
        """Create a focused Phase 4 task database"""
        print("📋 Creating Phase 4 Task Database...")
        
        database_data = {
            "parent": {"page_id": self.page_id},
            "title": [{"type": "text", "text": {"content": "Phase 4: Production Tasks"}}],
            "properties": {
                "Task": {
                    "title": {}
                },
                "Status": {
                    "select": {
                        "options": [
                            {"name": "Not Started", "color": "gray"},
                            {"name": "In Progress", "color": "yellow"},
                            {"name": "Complete", "color": "green"},
                            {"name": "Blocked", "color": "red"}
                        ]
                    }
                },
                "Priority": {
                    "select": {
                        "options": [
                            {"name": "Critical", "color": "red"},
                            {"name": "High", "color": "orange"},
                            {"name": "Medium", "color": "yellow"},
                            {"name": "Low", "color": "gray"}
                        ]
                    }
                },
                "Category": {
                    "select": {
                        "options": [
                            {"name": "HA Add-on Store", "color": "blue"},
                            {"name": "Monitoring", "color": "green"},
                            {"name": "Security", "color": "red"},
                            {"name": "Documentation", "color": "purple"}
                        ]
                    }
                },
                "Assignee": {
                    "rich_text": {}
                },
                "Due Date": {
                    "date": {}
                }
            }
        }
        
        response = requests.post('https://api.notion.com/v1/databases', 
                               headers=self.headers, 
                               json=database_data)
        
        if response.status_code == 200:
            db_id = response.json()['id']
            print(f"✅ Created Phase 4 Task Database: {db_id}")
            
            # Add initial tasks
            self.populate_phase4_tasks(db_id)
            return db_id
        else:
            print(f"❌ Failed to create database: {response.status_code}")
            return None
    
    def populate_phase4_tasks(self, db_id):
        """Populate the Phase 4 database with initial tasks"""
        print("📝 Adding initial Phase 4 tasks...")
        
        tasks = [
            {
                "Task": "Update config.yaml with Phase 3 features",
                "Status": "In Progress",
                "Priority": "High",
                "Category": "HA Add-on Store"
            },
            {
                "Task": "Create comprehensive README.md for store submission",
                "Status": "Not Started",
                "Priority": "High",
                "Category": "HA Add-on Store"
            },
            {
                "Task": "Prepare DOCS.md with installation guides",
                "Status": "Not Started",
                "Priority": "High",
                "Category": "HA Add-on Store"
            },
            {
                "Task": "Update CHANGELOG.md with v2.0+ features",
                "Status": "Not Started",
                "Priority": "Medium",
                "Category": "HA Add-on Store"
            },
            {
                "Task": "Implement system health monitoring",
                "Status": "Not Started",
                "Priority": "High",
                "Category": "Monitoring"
            },
            {
                "Task": "Add performance metrics collection",
                "Status": "Not Started",
                "Priority": "Medium",
                "Category": "Monitoring"
            },
            {
                "Task": "Implement error tracking and alerting",
                "Status": "Not Started",
                "Priority": "Medium",
                "Category": "Monitoring"
            },
            {
                "Task": "Conduct security audit",
                "Status": "Not Started",
                "Priority": "High",
                "Category": "Security"
            },
            {
                "Task": "Enhance configuration validation",
                "Status": "Not Started",
                "Priority": "Medium",
                "Category": "Security"
            },
            {
                "Task": "Create production deployment guide",
                "Status": "Not Started",
                "Priority": "Medium",
                "Category": "Documentation"
            }
        ]
        
        for task_data in tasks:
            page_data = {
                "parent": {"database_id": db_id},
                "properties": {
                    "Task": {
                        "title": [{"type": "text", "text": {"content": task_data["Task"]}}]
                    },
                    "Status": {
                        "select": {"name": task_data["Status"]}
                    },
                    "Priority": {
                        "select": {"name": task_data["Priority"]}
                    },
                    "Category": {
                        "select": {"name": task_data["Category"]}
                    }
                }
            }
            
            response = requests.post('https://api.notion.com/v1/pages', 
                                   headers=self.headers, 
                                   json=page_data)
            
            if response.status_code == 200:
                print(f"✅ Added task: {task_data['Task']}")
            else:
                print(f"❌ Failed to add task: {task_data['Task']}")
    
    def add_detailed_sections(self):
        """Add detailed sections to the main page"""
        print("📄 Adding detailed sections...")
        
        sections = [
            # Key Metrics Section
            {
                "type": "heading_2",
                "heading_2": {
                    "rich_text": [{"type": "text", "text": {"content": "📊 Key Metrics & Status"}}]
                }
            },
            {
                "type": "callout",
                "callout": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "Test Success Rate: 98.8% (254/257 tests passing)\n"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": "Phase 3 Features: 100% test pass rate (33/33 tests)\n"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": "Integration Tests: 100% pass rate (10/10 tests)\n"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": "Performance: 0.001s average operation time\n"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": "Warnings: 0 (all resolved)"}, "annotations": {"bold": True}}
                    ],
                    "icon": {"emoji": "📊"},
                    "color": "blue_background"
                }
            },
            
            # Phase 3 Features Summary
            {
                "type": "heading_2",
                "heading_2": {
                    "rich_text": [{"type": "text", "text": {"content": "🎮 Phase 3 Features Implemented"}}]
                }
            },
            {
                "type": "bulleted_list_item",
                "bulleted_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "📱 Mobile Integration"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - PWA support, responsive design, touch controls, gesture navigation"}}
                    ]
                }
            },
            {
                "type": "bulleted_list_item",
                "bulleted_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "🎮 Gamification System"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - 12 achievements, leveling system, daily challenges, progress tracking"}}
                    ]
                }
            },
            {
                "type": "bulleted_list_item",
                "bulleted_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "🔔 Advanced Notifications"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Smart timing, multi-channel delivery, personalization, frequency limits"}}
                    ]
                }
            },
            
            # Next Steps Section
            {
                "type": "heading_2",
                "heading_2": {
                    "rich_text": [{"type": "text", "text": {"content": "🎯 Immediate Next Steps"}}]
                }
            },
            {
                "type": "numbered_list_item",
                "numbered_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "Complete HA Add-on Store preparation"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Update config.yaml, create README.md, prepare documentation"}}
                    ]
                }
            },
            {
                "type": "numbered_list_item",
                "numbered_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "Implement monitoring and observability"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Health checks, performance metrics, error tracking"}}
                    ]
                }
            },
            {
                "type": "numbered_list_item",
                "numbered_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "Production hardening and security"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Security audit, configuration validation, deployment procedures"}}
                    ]
                }
            },
            
            # Resources Section
            {
                "type": "heading_2",
                "heading_2": {
                    "rich_text": [{"type": "text", "text": {"content": "📚 Key Resources"}}]
                }
            },
            {
                "type": "bulleted_list_item",
                "bulleted_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "📋 PROJECT_HANDOFF_SUMMARY.md"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Complete project status and handoff information"}}
                    ]
                }
            },
            {
                "type": "bulleted_list_item",
                "bulleted_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "🎯 NEXT_AGENT_PROMPT_V4.md"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Comprehensive mission briefing for Phase 4"}}
                    ]
                }
            },
            {
                "type": "bulleted_list_item",
                "bulleted_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "📊 docs/TEST_STATUS_ANALYSIS.md"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Detailed test analysis and production readiness assessment"}}
                    ]
                }
            },
            {
                "type": "bulleted_list_item",
                "bulleted_list_item": {
                    "rich_text": [
                        {"type": "text", "text": {"content": "🎉 docs/PHASE3_COMPLETION_SUMMARY.md"}, "annotations": {"bold": True}},
                        {"type": "text", "text": {"content": " - Complete Phase 3 achievements and feature documentation"}}
                    ]
                }
            }
        ]
        
        # Add sections to page
        response = requests.patch(f'https://api.notion.com/v1/blocks/{self.page_id}/children',
                                headers=self.headers,
                                json={"children": sections})
        
        if response.status_code == 200:
            print("✅ Added detailed sections")
            return True
        else:
            print(f"❌ Failed to add sections: {response.status_code}")
            return False
    
    def build_structured_content(self):
        """Main method to build all structured content"""
        print("🏗️ BUILDING STRUCTURED CONTENT")
        print("=" * 50)
        
        try:
            # Create Phase 4 task database
            db_id = self.create_phase4_task_database()
            
            # Add detailed sections
            if self.add_detailed_sections():
                print("\n🎉 STRUCTURED CONTENT CREATION COMPLETE!")
                print("=" * 50)
                print("✅ Phase 4 task database created and populated")
                print("✅ Detailed sections added with metrics and resources")
                print("✅ Clear next steps outlined for incoming agent")
                print("✅ Workspace is now production-ready and well-organized")
                return True
            else:
                return False
                
        except Exception as e:
            print(f"❌ Error building structured content: {e}")
            return False

def main():
    """Main execution function"""
    builder = NotionContentBuilder()
    success = builder.build_structured_content()
    
    if success:
        print("\n🚀 Notion workspace is now fully reorganized and ready!")
        print("The incoming agent has everything needed to continue Phase 4 development.")
    else:
        print("\n❌ Content building failed - manual intervention may be required")

if __name__ == "__main__":
    main()
