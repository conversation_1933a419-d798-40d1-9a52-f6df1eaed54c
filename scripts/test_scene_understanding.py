#!/usr/bin/env python3
"""
Test script for Advanced Scene Understanding system in AICleaner
Tests room detection, object recognition, seasonal adjustments, and contextual insights
"""

import os
import sys
import json
from datetime import datetime, timezone

# Add the aicleaner directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'aicleaner'))

def test_scene_understanding_system():
    """Test the advanced scene understanding system"""
    print("🧠 AICleaner Advanced Scene Understanding Test")
    print("=" * 55)
    
    try:
        from scene_understanding import AdvancedSceneUnderstanding, RoomType, Season, TimeOfDay, SceneContext
        print("✅ Scene understanding modules imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import scene understanding modules: {e}")
        return False
    
    # Test 1: System initialization
    print("\n🚀 Test 1: System Initialization")
    
    try:
        scene_ai = AdvancedSceneUnderstanding()
        print("✅ Advanced scene understanding system initialized")
        print(f"   Room patterns loaded: {len(scene_ai.room_patterns)}")
        print(f"   Object database loaded: {len(scene_ai.object_database)}")
        print(f"   Seasonal adjustments loaded: {len(scene_ai.seasonal_adjustments)}")
    except Exception as e:
        print(f"❌ Failed to initialize scene understanding: {e}")
        return False
    
    # Test 2: Room type detection
    print("\n🏠 Test 2: Room Type Detection")
    
    test_cases = [
        ("kitchen", "cooking and food preparation", ["stove", "refrigerator", "sink"], RoomType.KITCHEN),
        ("master_bathroom", "personal hygiene and bathing", ["toilet", "shower", "sink"], RoomType.BATHROOM),
        ("living_room", "family relaxation and entertainment", ["sofa", "tv", "coffee table"], RoomType.LIVING_ROOM),
        ("home_office", "work and study space", ["desk", "computer", "chair"], RoomType.OFFICE),
        ("bedroom", "sleeping and rest", ["bed", "dresser", "nightstand"], RoomType.BEDROOM),
        ("unknown_space", "general purpose", ["random", "items"], RoomType.UNKNOWN)
    ]
    
    for zone_name, zone_purpose, objects, expected_room in test_cases:
        detected_room = scene_ai.detect_room_type(zone_name, zone_purpose, objects)
        status = "✅" if detected_room == expected_room else "❌"
        print(f"   {status} {zone_name}: {detected_room.value} (expected: {expected_room.value})")
    
    # Test 3: Temporal context
    print("\n⏰ Test 3: Temporal Context Detection")
    
    try:
        current_season = scene_ai.get_current_season()
        current_time = scene_ai.get_time_of_day()
        print(f"✅ Current season: {current_season.value}")
        print(f"✅ Current time of day: {current_time.value}")
    except Exception as e:
        print(f"❌ Temporal context detection failed: {e}")
        return False
    
    # Test 4: Object extraction from AI response
    print("\n🔍 Test 4: Object Extraction from AI Response")
    
    test_ai_response = """
    The kitchen appears moderately clean. The countertop has some items scattered on it, 
    and the sink has a few dishes. The stove looks clean, and the refrigerator door 
    has some fingerprints. The floor could use a quick sweep, and the microwave 
    needs wiping down. Overall, the space is functional but could benefit from 
    some organization and surface cleaning.
    """
    
    try:
        extracted_objects = scene_ai.extract_objects_from_analysis(test_ai_response)
        print(f"✅ Extracted {len(extracted_objects)} objects from AI response")
        print(f"   Objects: {', '.join(extracted_objects)}")
    except Exception as e:
        print(f"❌ Object extraction failed: {e}")
        return False
    
    # Test 5: Scene context analysis
    print("\n🎬 Test 5: Scene Context Analysis")
    
    try:
        scene_context = scene_ai.analyze_scene_context(
            zone_name="kitchen",
            zone_purpose="cooking and food preparation",
            ai_response=test_ai_response
        )
        
        print("✅ Scene context analysis completed")
        print(f"   Room type: {scene_context.room_type.value}")
        print(f"   Detected objects: {len(scene_context.detected_objects)}")
        print(f"   Lighting: {scene_context.lighting_condition}")
        print(f"   Season: {scene_context.season.value}")
        print(f"   Time of day: {scene_context.time_of_day.value}")
        print(f"   Cleanliness indicators: {scene_context.cleanliness_indicators}")
    except Exception as e:
        print(f"❌ Scene context analysis failed: {e}")
        return False
    
    # Test 6: Contextual insights generation
    print("\n💡 Test 6: Contextual Insights Generation")
    
    try:
        insights = scene_ai.generate_contextual_insights(
            scene_context=scene_context,
            ai_analysis={'response': test_ai_response}
        )
        
        print(f"✅ Generated {len(insights)} contextual insights")
        for i, insight in enumerate(insights, 1):
            print(f"   {i}. {insight.insight_type}: {insight.description}")
            print(f"      Confidence: {insight.confidence:.2%}")
            print(f"      Room specific: {insight.room_specific}")
            print(f"      Seasonal: {insight.seasonal_relevance}")
            print(f"      Time sensitive: {insight.time_sensitive}")
    except Exception as e:
        print(f"❌ Contextual insights generation failed: {e}")
        return False
    
    # Test 7: Prompt enhancement
    print("\n📝 Test 7: AI Prompt Enhancement")
    
    base_prompt = "Analyze this image and provide cleaning recommendations."
    
    try:
        enhanced_prompt = scene_ai.enhance_ai_prompt(base_prompt, scene_context)
        print("✅ AI prompt enhanced with context")
        print(f"   Original length: {len(base_prompt)} characters")
        print(f"   Enhanced length: {len(enhanced_prompt)} characters")
        print(f"   Context added: {len(enhanced_prompt) - len(base_prompt)} characters")
        
        # Show a snippet of the enhancement
        enhancement = enhanced_prompt[len(base_prompt):]
        print(f"   Enhancement preview: {enhancement[:100]}...")
    except Exception as e:
        print(f"❌ Prompt enhancement failed: {e}")
        return False
    
    # Test 8: Task prioritization with context
    print("\n📋 Test 8: Task Prioritization with Context")
    
    test_tasks = [
        {'description': 'Wipe down countertop', 'priority': 5, 'category': 'cleaning'},
        {'description': 'Organize pantry', 'priority': 3, 'category': 'organization'},
        {'description': 'Clean microwave', 'priority': 4, 'category': 'cleaning'},
        {'description': 'Sweep floor', 'priority': 6, 'category': 'cleaning'},
        {'description': 'Sanitize sink', 'priority': 7, 'category': 'cleaning'}
    ]
    
    try:
        prioritized_tasks = scene_ai.prioritize_tasks_with_context(test_tasks, scene_context)
        print(f"✅ Prioritized {len(prioritized_tasks)} tasks with context")
        
        print("   Task priority changes:")
        for i, task in enumerate(prioritized_tasks):
            original = task.get('original_priority', task['priority'])
            current = task['priority']
            boost = task.get('context_boost', 0)
            change = "↑" if current > original else "→" if current == original else "↓"
            print(f"   {i+1}. {task['description']}: {original} → {current} {change} (boost: +{boost:.1f})")
    except Exception as e:
        print(f"❌ Task prioritization failed: {e}")
        return False
    
    # Test 9: Seasonal adjustments
    print("\n🌱 Test 9: Seasonal Adjustments")
    
    try:
        for season in Season:
            adjustments = scene_ai.seasonal_adjustments[season]
            print(f"   {season.value.title()}:")
            print(f"     Focus areas: {', '.join(adjustments['focus_areas'])}")
            print(f"     Frequency multiplier: {adjustments['frequency_multiplier']}")
            print(f"     Special tasks: {len(adjustments['special_tasks'])}")
    except Exception as e:
        print(f"❌ Seasonal adjustments test failed: {e}")
        return False
    
    # Test 10: Context summary
    print("\n📊 Test 10: Context Summary")
    
    try:
        context_summary = scene_ai.get_context_summary(scene_context)
        print("✅ Context summary generated")
        print(f"   Room type: {context_summary['room_type']}")
        print(f"   Season: {context_summary['season']}")
        print(f"   Time of day: {context_summary['time_of_day']}")
        print(f"   Objects detected: {context_summary['detected_objects_count']}")
        print(f"   Cleanliness indicators: {len(context_summary['cleanliness_indicators'])}")
        print(f"   Seasonal frequency multiplier: {context_summary['seasonal_frequency_multiplier']}")
    except Exception as e:
        print(f"❌ Context summary failed: {e}")
        return False
    
    print("\n🎉 Advanced Scene Understanding Test Completed Successfully!")
    return True


def test_aicleaner_integration():
    """Test AICleaner integration with scene understanding"""
    print("\n🏠 AICleaner Integration Test")
    print("=" * 40)
    
    # Test configuration with scene understanding enabled
    test_config = {
        'gemini_api_key': os.getenv('GEMINI_API_KEY', 'test_key'),
        'enable_scene_understanding': True,
        'display_name': 'Test User',
        'zones': [{
            'name': 'test_kitchen',
            'icon': 'mdi:chef-hat',
            'purpose': 'cooking and food preparation',
            'camera_entity': 'camera.test',
            'todo_list_entity': 'todo.test',
            'update_frequency': 30,
            'notifications_enabled': False,
            'notification_service': '',
            'notification_personality': 'default',
            'notify_on_create': False,
            'notify_on_complete': False
        }]
    }
    
    print(f"✅ Test configuration prepared")
    print(f"   Scene understanding: {test_config['enable_scene_understanding']}")
    print(f"   Test zone: {test_config['zones'][0]['name']}")
    print(f"   Zone purpose: {test_config['zones'][0]['purpose']}")
    
    # Test scene understanding methods would be available in AICleaner
    expected_methods = [
        'analyze_scene_context',
        'enhance_analysis_with_context',
        'prioritize_tasks_with_context'
    ]
    
    print(f"✅ Expected AICleaner methods: {', '.join(expected_methods)}")
    
    return True


if __name__ == "__main__":
    print("🚀 Starting AICleaner Advanced Scene Understanding Tests")
    print("=" * 65)
    
    success = True
    
    # Run scene understanding system tests
    if not test_scene_understanding_system():
        success = False
    
    # Run AICleaner integration tests
    if not test_aicleaner_integration():
        success = False
    
    print("\n" + "=" * 65)
    if success:
        print("🎉 All tests completed successfully!")
        print("✅ Advanced scene understanding system is ready for production use")
    else:
        print("❌ Some tests failed - please review the output above")
        sys.exit(1)
