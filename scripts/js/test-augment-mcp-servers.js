#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');

console.log('🔧 Testing Augment MCP Servers\n');

// Load MCP configuration
const mcpConfig = JSON.parse(fs.readFileSync('../../.roo/mcp.json', 'utf8'));

const results = {
    passed: [],
    failed: [],
    total: 0
};

// Test a single MCP server
function testServer(name, serverConfig) {
    return new Promise((resolve) => {
        console.log(`Testing ${name}...`);
        
        const serverProcess = spawn(serverConfig.command, serverConfig.args, {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { 
                ...process.env, 
                ...serverConfig.env
            }
        });

        let output = '';
        let error = '';
        let resolved = false;

        serverProcess.stdout.on('data', (data) => {
            output += data.toString();
        });

        serverProcess.stderr.on('data', (data) => {
            error += data.toString();
        });

        // Send JSON-RPC request after server starts
        setTimeout(() => {
            if (!resolved && serverProcess.pid) {
                const testRequest = JSON.stringify({
                    jsonrpc: "2.0",
                    id: 1,
                    method: "tools/list",
                    params: {}
                }) + '\n';
                
                try {
                    serverProcess.stdin.write(testRequest);
                } catch (err) {
                    // Ignore write errors
                }
            }
        }, 1000);

        // Give server 8 seconds to respond
        setTimeout(() => {
            if (!resolved) {
                resolved = true;
                serverProcess.kill('SIGTERM');
                
                // Check if server responded with tools
                const hasTools = output.includes('tools') || output.includes('result');
                const hasError = error.includes('Error:') || error.includes('ENOENT') || 
                               error.includes('required') || error.includes('not found');
                
                if (hasTools && !hasError) {
                    console.log(`  ✅ ${name}: Working`);
                    results.passed.push(name);
                } else {
                    console.log(`  ❌ ${name}: Failed`);
                    if (error.includes('required')) {
                        console.log(`     Issue: Missing required environment variables`);
                    } else if (hasError) {
                        console.log(`     Error: ${error.substring(0, 100)}...`);
                    } else {
                        console.log(`     Issue: No tools response received`);
                    }
                    results.failed.push(name);
                }
                resolve();
            }
        }, 8000);

        serverProcess.on('error', (err) => {
            if (!resolved) {
                resolved = true;
                console.log(`  ❌ ${name}: Process error - ${err.message}`);
                results.failed.push(name);
                resolve();
            }
        });
    });
}

// Test all servers
async function runTests() {
    console.log('Testing all configured MCP servers...\n');
    
    const servers = Object.entries(mcpConfig.mcpServers);
    results.total = servers.length;
    
    for (const [name, config] of servers) {
        await testServer(name, config);
    }
    
    // Print summary
    console.log('\n📊 Test Results Summary:');
    console.log(`Total servers tested: ${results.total}`);
    console.log(`✅ Passed: ${results.passed.length}`);
    console.log(`❌ Failed: ${results.failed.length}`);
    
    if (results.passed.length > 0) {
        console.log('\n✅ Working servers:');
        results.passed.forEach(name => console.log(`  - ${name}`));
    }
    
    if (results.failed.length > 0) {
        console.log('\n❌ Failed servers:');
        results.failed.forEach(name => console.log(`  - ${name}`));
    }
    
    console.log('\n🎉 MCP server testing completed!');
    
    if (results.passed.length === results.total) {
        console.log('🚀 All servers are working! Augment should now have access to MCP tools.');
    } else {
        console.log('⚠️  Some servers need configuration. Check environment variables and API keys.');
    }
}

runTests().catch(console.error);
