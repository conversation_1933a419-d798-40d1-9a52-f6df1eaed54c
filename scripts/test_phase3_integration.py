#!/usr/bin/env python3
"""
Comprehensive integration test for AICleaner Phase 3 features
Tests mobile integration, gamification, and advanced notifications working together
"""

import os
import sys
import json
import tempfile
from datetime import datetime, timezone

# Add the aicleaner directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'aicleaner'))

def test_phase3_integration():
    """Test Phase 3 features integration"""
    print("🚀 AICleaner Phase 3 Integration Test")
    print("=" * 50)
    
    try:
        from mobile_integration import MobileIntegration
        from gamification import GamificationSystem
        from advanced_notifications import AdvancedNotificationSystem
        print("✅ All Phase 3 modules imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Phase 3 modules: {e}")
        return False
    
    # Test 1: Initialize all systems
    print("\n🚀 Test 1: System Initialization")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Initialize all three systems
            mobile_dir = os.path.join(temp_dir, "mobile")
            gamification_dir = os.path.join(temp_dir, "gamification")
            notifications_dir = os.path.join(temp_dir, "notifications")
            
            mobile_system = MobileIntegration(config_path=mobile_dir)
            gamification_system = GamificationSystem(data_path=gamification_dir)
            notification_system = AdvancedNotificationSystem(data_path=notifications_dir)
            
            print("✅ All systems initialized successfully")
            print(f"   Mobile integration: ✓")
            print(f"   Gamification system: ✓")
            print(f"   Advanced notifications: ✓")
        except Exception as e:
            print(f"❌ System initialization failed: {e}")
            return False
        
        # Test 2: Mobile configuration integration
        print("\n📱 Test 2: Mobile Configuration Integration")
        
        try:
            # Configure mobile for gamification and notifications
            mobile_config = {
                'enable_push_notifications': True,
                'enable_quick_actions': True,
                'enable_gesture_controls': True,
                'theme_preference': 'dark',
                'compact_mode': True
            }
            
            mobile_system.update_mobile_config(mobile_config)
            
            # Add gamification quick actions
            gamification_actions = [
                {
                    'id': 'view_achievements',
                    'title': 'View Achievements',
                    'icon': 'mdi:trophy',
                    'action_type': 'navigation',
                    'target': 'achievements',
                    'parameters': {}
                },
                {
                    'id': 'daily_challenges',
                    'title': 'Daily Challenges',
                    'icon': 'mdi:target',
                    'action_type': 'navigation',
                    'target': 'challenges',
                    'parameters': {}
                }
            ]
            
            for action in gamification_actions:
                mobile_system.add_quick_action(action)
            
            quick_actions = mobile_system.get_quick_actions()
            print(f"✅ Mobile configuration integrated")
            print(f"   Push notifications: {mobile_config['enable_push_notifications']}")
            print(f"   Quick actions: {len(quick_actions)}")
            print(f"   Gamification actions added: 2")
            
        except Exception as e:
            print(f"❌ Mobile configuration integration failed: {e}")
            return False
        
        # Test 3: Gamification with notifications
        print("\n🎮 Test 3: Gamification with Notifications")
        
        try:
            # Configure notifications for gamification events
            notification_prefs = {
                'enabled_channels': ['home_assistant', 'mobile_push'],
                'personalization_enabled': True,
                'do_not_disturb': False
            }
            notification_system.update_user_preferences(notification_prefs)
            
            # Simulate task completions to trigger gamification and notifications
            completion_time = datetime.now(timezone.utc)
            
            # Complete several tasks to unlock achievements
            for i in range(3):
                gamification_system.record_task_completion(
                    zone_name=f"test_zone_{i % 2}",
                    task_description=f"Test task {i+1}",
                    completion_time=completion_time,
                    task_score=0.9
                )
                
                # Send corresponding notifications
                motivational_msg = gamification_system.get_motivational_message()
                notification_system.send_notification(
                    event_type="task_completed",
                    variables={
                        "zone_name": f"test_zone_{i % 2}",
                        "task_description": f"Test task {i+1}",
                        "motivational_message": motivational_msg['message']
                    }
                )
            
            # Check for achievement unlocks and send notifications
            user_stats = gamification_system.get_user_stats()
            achievements = gamification_system.get_achievements()
            unlocked_achievements = [a for a in achievements if a['unlocked']]
            
            for achievement in unlocked_achievements:
                notification_system.send_notification(
                    event_type="achievement_unlocked",
                    variables={
                        "achievement_title": achievement['title'],
                        "achievement_description": achievement['description']
                    }
                )
            
            print(f"✅ Gamification with notifications working")
            print(f"   Tasks completed: {user_stats['total_tasks_completed']}")
            print(f"   Achievements unlocked: {len(unlocked_achievements)}")
            print(f"   Notifications sent: {len(notification_system.get_notification_history())}")
            
        except Exception as e:
            print(f"❌ Gamification with notifications failed: {e}")
            return False
        
        # Test 4: Mobile layout optimization for gamification
        print("\n📐 Test 4: Mobile Layout Optimization")
        
        try:
            # Get mobile layouts for different screen sizes
            mobile_layout = mobile_system.get_mobile_optimized_layout("mobile")
            tablet_layout = mobile_system.get_mobile_optimized_layout("tablet")
            
            # Verify layouts are optimized for gamification content
            print(f"✅ Mobile layouts optimized")
            print(f"   Mobile grid columns: {mobile_layout['grid_columns']}")
            print(f"   Mobile button height: {mobile_layout['button_height']}")
            print(f"   Mobile compact mode: {mobile_layout['compact_mode']}")
            print(f"   Tablet grid columns: {tablet_layout['grid_columns']}")
            print(f"   Max items per view: {mobile_layout['max_items_per_view']}")
            
        except Exception as e:
            print(f"❌ Mobile layout optimization failed: {e}")
            return False
        
        # Test 5: Progressive Web App manifest with gamification
        print("\n📲 Test 5: PWA Manifest with Gamification")
        
        try:
            manifest = mobile_system.generate_mobile_manifest()
            
            # Verify PWA manifest includes gamification features
            print(f"✅ PWA manifest generated")
            print(f"   App name: {manifest['name']}")
            print(f"   Display mode: {manifest['display']}")
            print(f"   Categories: {', '.join(manifest['categories'])}")
            print(f"   Icons: {len(manifest['icons'])}")
            
        except Exception as e:
            print(f"❌ PWA manifest generation failed: {e}")
            return False
        
        # Test 6: Notification analytics and mobile stats
        print("\n📊 Test 6: Analytics Integration")
        
        try:
            # Get notification statistics
            notification_stats = notification_system.get_notification_stats()
            
            # Get gamification progress
            progress_summary = gamification_system.get_progress_summary()
            
            # Get mobile system status
            mobile_status = mobile_system.get_system_status()
            
            print(f"✅ Analytics integration working")
            print(f"   Notifications sent: {notification_stats['total_sent']}")
            print(f"   Notification success rate: {notification_stats['success_rate']:.1%}")
            print(f"   User level: {progress_summary['level']}")
            print(f"   Current streak: {progress_summary['current_streak']}")
            print(f"   Mobile features enabled: {len(mobile_status['enabled_features'])}")
            
        except Exception as e:
            print(f"❌ Analytics integration failed: {e}")
            return False
        
        # Test 7: Cross-system data consistency
        print("\n🔄 Test 7: Cross-System Data Consistency")
        
        try:
            # Verify data consistency across systems
            mobile_config_data = mobile_system.get_mobile_config()
            gamification_data = gamification_system.export_gamification_data()
            notification_prefs = notification_system.get_user_preferences()

            # Check that mobile push notifications are enabled in both systems
            mobile_push_enabled = 'mobile_push' in notification_prefs['enabled_channels']
            mobile_notifications_enabled = mobile_config_data['enable_push_notifications']
            
            print(f"✅ Cross-system data consistency verified")
            print(f"   Mobile push in notifications: {mobile_push_enabled}")
            print(f"   Mobile notifications enabled: {mobile_notifications_enabled}")
            print(f"   Data consistency: {'✓' if mobile_push_enabled == mobile_notifications_enabled else '✗'}")
            
        except Exception as e:
            print(f"❌ Cross-system data consistency failed: {e}")
            return False
        
        # Test 8: Complete user workflow simulation
        print("\n🎯 Test 8: Complete User Workflow Simulation")
        
        try:
            # Simulate a complete user workflow
            workflow_steps = []
            
            # Step 1: User opens mobile app
            mobile_layout = mobile_system.get_mobile_optimized_layout("mobile")
            workflow_steps.append("Mobile app opened")
            
            # Step 2: User completes a task
            gamification_system.record_task_completion(
                zone_name="Living Room",
                task_description="Vacuum carpet",
                completion_time=datetime.now(timezone.utc),
                task_score=1.0
            )
            workflow_steps.append("Task completed")
            
            # Step 3: Achievement notification sent
            notification_system.send_notification(
                event_type="task_completed",
                variables={
                    "zone_name": "Living Room",
                    "task_description": "Vacuum carpet",
                    "motivational_message": "Perfect score! Excellent work!"
                }
            )
            workflow_steps.append("Notification sent")
            
            # Step 4: User checks progress on mobile
            progress = gamification_system.get_progress_summary()
            workflow_steps.append("Progress checked")
            
            # Step 5: User views achievements
            achievements = gamification_system.get_achievements()
            workflow_steps.append("Achievements viewed")
            
            print(f"✅ Complete user workflow simulated")
            for i, step in enumerate(workflow_steps, 1):
                print(f"   {i}. {step}")
            
        except Exception as e:
            print(f"❌ Complete user workflow simulation failed: {e}")
            return False
        
        # Test 9: Performance and resource usage
        print("\n⚡ Test 9: Performance and Resource Usage")
        
        try:
            import time
            
            # Measure performance of integrated operations
            start_time = time.time()
            
            # Perform multiple operations
            for i in range(5):
                gamification_system.record_task_completion(
                    zone_name=f"zone_{i}",
                    task_description=f"task_{i}",
                    completion_time=datetime.now(timezone.utc),
                    task_score=0.8
                )
                
                notification_system.send_notification(
                    event_type="task_completed",
                    variables={
                        "zone_name": f"zone_{i}",
                        "task_description": f"task_{i}",
                        "motivational_message": "Great job!"
                    }
                )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            print(f"✅ Performance test completed")
            print(f"   Operations: 10 (5 gamification + 5 notifications)")
            print(f"   Total time: {total_time:.3f} seconds")
            print(f"   Average per operation: {total_time/10:.3f} seconds")
            print(f"   Performance: {'✓' if total_time < 1.0 else '⚠️'}")
            
        except Exception as e:
            print(f"❌ Performance test failed: {e}")
            return False
        
        # Test 10: System health and status
        print("\n🏥 Test 10: System Health and Status")
        
        try:
            # Get status from all systems
            mobile_status = mobile_system.get_system_status()
            gamification_status = gamification_system.get_system_status()
            notification_status = notification_system.get_system_status()
            
            all_healthy = (
                mobile_status['mobile_config_loaded'] and
                gamification_status['system_health'] == 'healthy' and
                notification_status['system_health'] == 'healthy'
            )
            
            print(f"✅ System health check completed")
            print(f"   Mobile system: {'✓' if mobile_status['mobile_config_loaded'] else '✗'}")
            print(f"   Gamification system: {'✓' if gamification_status['system_health'] == 'healthy' else '✗'}")
            print(f"   Notification system: {'✓' if notification_status['system_health'] == 'healthy' else '✗'}")
            print(f"   Overall health: {'✓ Healthy' if all_healthy else '⚠️ Issues detected'}")
            
        except Exception as e:
            print(f"❌ System health check failed: {e}")
            return False
    
    print("\n🎉 Phase 3 Integration Test Completed Successfully!")
    return True


if __name__ == "__main__":
    print("🚀 Starting AICleaner Phase 3 Integration Tests")
    print("=" * 60)
    
    success = test_phase3_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All Phase 3 integration tests completed successfully!")
        print("✅ Mobile integration, gamification, and advanced notifications")
        print("   are working together seamlessly!")
        print("\n🚀 Phase 3 is ready for production deployment!")
    else:
        print("❌ Some integration tests failed - please review the output above")
        sys.exit(1)
