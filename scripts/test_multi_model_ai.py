#!/usr/bin/env python3
"""
Test script for Multi-Model AI integration in AICleaner
Tests Gemini, Claude, and GPT-4V integration with fallback mechanisms
"""

import os
import sys
import json
import tempfile
from datetime import datetime

# Add the aicleaner directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'aicleaner'))

def test_multi_model_ai_system():
    """Test the multi-model AI system"""
    print("🤖 AICleaner Multi-Model AI Integration Test")
    print("=" * 60)
    
    try:
        from multi_model_ai import MultiModelAIOptimizer, AIModel, GeminiProvider, ClaudeProvider, GPTProvider
        print("✅ Multi-model AI modules imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import multi-model AI modules: {e}")
        return False
    
    # Test 1: Provider availability
    print("\n🔧 Test 1: Provider Availability")
    
    # Check Gemini
    gemini_key = os.getenv('GEMINI_API_KEY')
    if gemini_key:
        try:
            gemini_provider = GeminiProvider(gemini_key)
            print(f"✅ Gemini provider: {'Available' if gemini_provider.is_available() else 'Not available'}")
        except Exception as e:
            print(f"⚠️  Gemini provider error: {e}")
    else:
        print("⚠️  Gemini API key not found")
    
    # Check Claude
    claude_key = os.getenv('CLAUDE_API_KEY')
    if claude_key:
        try:
            claude_provider = ClaudeProvider(claude_key)
            print(f"✅ Claude provider: {'Available' if claude_provider.is_available() else 'Not available'}")
        except Exception as e:
            print(f"⚠️  Claude provider error: {e}")
    else:
        print("⚠️  Claude API key not found")
    
    # Check GPT-4V
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        try:
            gpt_provider = GPTProvider(openai_key)
            print(f"✅ GPT-4V provider: {'Available' if gpt_provider.is_available() else 'Not available'}")
        except Exception as e:
            print(f"⚠️  GPT-4V provider error: {e}")
    else:
        print("⚠️  OpenAI API key not found")
    
    # Test 2: Multi-model optimizer initialization
    print("\n🚀 Test 2: Multi-Model Optimizer Initialization")
    
    config = {}
    if gemini_key:
        config['gemini_api_key'] = gemini_key
    if claude_key:
        config['claude_api_key'] = claude_key
    if openai_key:
        config['openai_api_key'] = openai_key
    
    if not config:
        print("❌ No API keys available for testing")
        return False
    
    try:
        optimizer = MultiModelAIOptimizer(config, cache_ttl=300)
        print(f"✅ Multi-model optimizer initialized")
        print(f"   Available models: {[model.value for model in optimizer.get_available_models()]}")
        print(f"   Preferred models: {[model.value for model in optimizer.preferred_models]}")
    except Exception as e:
        print(f"❌ Failed to initialize multi-model optimizer: {e}")
        return False
    
    # Test 3: System status
    print("\n📊 Test 3: System Status")
    try:
        status = optimizer.get_system_status()
        print(f"✅ System status retrieved:")
        print(f"   Health: {status['system_health']}")
        print(f"   Available models: {status['available_models']}")
        print(f"   Providers initialized: {status['providers_initialized']}")
        print(f"   Cache entries: {status['cache_stats']['total_entries']}")
    except Exception as e:
        print(f"❌ Failed to get system status: {e}")
        return False
    
    # Test 4: Mock analysis (if we have a test image)
    print("\n🖼️  Test 4: Mock Analysis Test")
    
    # Create a simple test image
    try:
        from PIL import Image
        import numpy as np
        
        # Create a simple test image
        test_image = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            test_image.save(tmp_file.name)
            test_image_path = tmp_file.name
        
        # Test batch analysis
        test_tasks = [
            {'id': 'test_1', 'description': 'Clean the counter', 'status': 'active'},
            {'id': 'test_2', 'description': 'Organize items', 'status': 'active'}
        ]
        
        result, was_cached = optimizer.analyze_batch_optimized(
            image_path=test_image_path,
            zone_name="Test Kitchen",
            zone_purpose="Kitchen cleaning and organization",
            active_tasks=test_tasks,
            ignore_rules=["vacuum", "mop"]
        )
        
        if result:
            print("✅ Mock analysis completed successfully")
            print(f"   Was cached: {was_cached}")
            print(f"   Model used: {result.get('analysis_metadata', {}).get('model_used', 'unknown')}")
            print(f"   Analysis type: {result.get('analysis_metadata', {}).get('analysis_type', 'unknown')}")
            
            # Test cache functionality
            result2, was_cached2 = optimizer.analyze_batch_optimized(
                image_path=test_image_path,
                zone_name="Test Kitchen",
                zone_purpose="Kitchen cleaning and organization",
                active_tasks=test_tasks,
                ignore_rules=["vacuum", "mop"]
            )
            
            if was_cached2:
                print("✅ Cache functionality working correctly")
            else:
                print("⚠️  Cache not working as expected")
        else:
            print("❌ Mock analysis failed")
        
        # Clean up test image
        os.unlink(test_image_path)
        
    except Exception as e:
        print(f"❌ Mock analysis test failed: {e}")
        return False
    
    # Test 5: Performance tracking
    print("\n📈 Test 5: Performance Tracking")
    try:
        perf_stats = optimizer.get_model_performance_stats()
        print("✅ Performance statistics retrieved:")
        for model, stats in perf_stats.items():
            print(f"   {model}: {stats['total_calls']} calls, {stats['success_rate']:.2%} success rate")
    except Exception as e:
        print(f"❌ Performance tracking test failed: {e}")
        return False
    
    print("\n🎉 Multi-Model AI Integration Test Completed Successfully!")
    return True


def test_aicleaner_integration():
    """Test AICleaner integration with multi-model AI"""
    print("\n🏠 AICleaner Integration Test")
    print("=" * 40)
    
    # Test configuration with multi-model AI enabled
    test_config = {
        'gemini_api_key': os.getenv('GEMINI_API_KEY', 'test_key'),
        'enable_multi_model_ai': True,
        'ai_model_preference': 'gemini,claude,gpt',
        'display_name': 'Test User',
        'zones': [{
            'name': 'test_kitchen',
            'icon': 'mdi:chef-hat',
            'purpose': 'Test kitchen zone',
            'camera_entity': 'camera.test',
            'todo_list_entity': 'todo.test',
            'update_frequency': 30,
            'notifications_enabled': False,
            'notification_service': '',
            'notification_personality': 'default',
            'notify_on_create': False,
            'notify_on_complete': False
        }]
    }
    
    # Add optional API keys if available
    if os.getenv('CLAUDE_API_KEY'):
        test_config['claude_api_key'] = os.getenv('CLAUDE_API_KEY')
    if os.getenv('OPENAI_API_KEY'):
        test_config['openai_api_key'] = os.getenv('OPENAI_API_KEY')
    
    print(f"✅ Test configuration prepared")
    print(f"   Multi-model AI: {test_config['enable_multi_model_ai']}")
    print(f"   Model preference: {test_config['ai_model_preference']}")
    print(f"   Available keys: {[k for k in test_config.keys() if 'api_key' in k]}")
    
    return True


if __name__ == "__main__":
    print("🚀 Starting AICleaner Multi-Model AI Tests")
    print("=" * 60)
    
    success = True
    
    # Run multi-model AI system tests
    if not test_multi_model_ai_system():
        success = False
    
    # Run AICleaner integration tests
    if not test_aicleaner_integration():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests completed successfully!")
        print("✅ Multi-model AI system is ready for production use")
    else:
        print("❌ Some tests failed - please review the output above")
        sys.exit(1)
