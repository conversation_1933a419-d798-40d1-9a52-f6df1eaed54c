#!/usr/bin/env python3
"""
Simple service handler for AICleaner that creates trigger files
when called via HTTP webhook.
"""

import json
import time
import os
from pathlib import Path
from flask import Flask, request, jsonify

app = Flask(__name__)

# Ensure trigger directory exists
TRIGGER_DIR = Path("/tmp/aicleaner_triggers")
TRIGGER_DIR.mkdir(exist_ok=True)

@app.route('/trigger', methods=['POST'])
def handle_trigger():
    """Handle service trigger requests."""
    try:
        data = request.get_json() or {}
        
        # Create trigger file
        trigger_data = {
            "action": data.get("action", "run_analysis"),
            "data": {
                "zone": data.get("zone", ""),
                "task_id": data.get("task_id", "")
            },
            "timestamp": time.time()
        }
        
        # Write trigger file
        trigger_file = TRIGGER_DIR / f"trigger_{int(time.time())}.json"
        with open(trigger_file, 'w') as f:
            json.dump(trigger_data, f)
        
        return jsonify({
            "status": "success",
            "message": f"Trigger created: {trigger_data['action']}",
            "trigger_file": str(trigger_file)
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/status', methods=['GET'])
def get_status():
    """Get service status."""
    return jsonify({
        "status": "running",
        "trigger_dir": str(TRIGGER_DIR),
        "trigger_files": len(list(TRIGGER_DIR.glob("trigger_*.json")))
    })

if __name__ == '__main__':
    print("Starting AICleaner Service Handler on port 8098")
    app.run(host='0.0.0.0', port=8098, debug=False)
