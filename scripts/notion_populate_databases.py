#!/usr/bin/env python3
"""
Populate the newly created databases with initial content
"""

import os
import requests
import json
from datetime import datetime, timezone

class NotionDatabasePopulator:
    def __init__(self):
        self.token = os.getenv('NOTION_TOKEN')
        self.page_id = '2202353b-33e4-8014-9b1f-d31d4cbb309d'
        self.headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json',
            'Notion-Version': '2022-06-28'
        }
    
    def get_database_ids(self):
        """Get the IDs of the newly created databases"""
        response = requests.get(f'https://api.notion.com/v1/blocks/{self.page_id}/children?page_size=100', 
                               headers=self.headers)
        
        if response.status_code != 200:
            return None
            
        blocks = response.json().get('results', [])
        databases = {}
        
        for block in blocks:
            if block.get('type') == 'child_database':
                title = block.get('child_database', {}).get('title', '')
                db_id = block.get('id')
                
                if 'Phase 4: Production Tasks' in title:
                    databases['tasks'] = db_id
                elif 'Configuration Database' in title:
                    databases['config'] = db_id
                elif 'Bug Tracker Database' in title:
                    databases['bugs'] = db_id
                elif 'Documentation Database' in title:
                    databases['docs'] = db_id
        
        return databases
    
    def populate_phase4_tasks(self, db_id):
        """Populate Phase 4 tasks database"""
        print("📋 Populating Phase 4 Tasks Database...")
        
        tasks = [
            {
                "Task": "Update config.yaml with Phase 3 features",
                "Status": "🔄 In Progress",
                "Priority": "⚡ High",
                "Category": "🏪 HA Add-on Store",
                "Notes": "Add mobile integration, gamification, and advanced notifications options to config.yaml"
            },
            {
                "Task": "Create comprehensive README.md for store submission",
                "Status": "⏳ Not Started",
                "Priority": "⚡ High",
                "Category": "🏪 HA Add-on Store",
                "Notes": "Include Phase 3 features, installation instructions, and feature highlights"
            },
            {
                "Task": "Prepare DOCS.md with installation guides",
                "Status": "⏳ Not Started",
                "Priority": "⚡ High",
                "Category": "🏪 HA Add-on Store",
                "Notes": "Comprehensive user documentation for all features including mobile and gamification"
            },
            {
                "Task": "Update CHANGELOG.md with v2.0+ features",
                "Status": "⏳ Not Started",
                "Priority": "📋 Medium",
                "Category": "🏪 HA Add-on Store",
                "Notes": "Document all Phase 1-3 achievements and feature additions"
            },
            {
                "Task": "Implement system health monitoring",
                "Status": "⏳ Not Started",
                "Priority": "⚡ High",
                "Category": "📊 Monitoring",
                "Notes": "Add health check endpoints and system status monitoring"
            },
            {
                "Task": "Add performance metrics collection",
                "Status": "⏳ Not Started",
                "Priority": "📋 Medium",
                "Category": "📊 Monitoring",
                "Notes": "Track AI analysis performance, response times, and resource usage"
            },
            {
                "Task": "Implement error tracking and alerting",
                "Status": "⏳ Not Started",
                "Priority": "📋 Medium",
                "Category": "📊 Monitoring",
                "Notes": "Comprehensive error logging and notification system"
            },
            {
                "Task": "Conduct security audit",
                "Status": "⏳ Not Started",
                "Priority": "⚡ High",
                "Category": "🔒 Security",
                "Notes": "Review API key handling, input validation, and access controls"
            },
            {
                "Task": "Enhance configuration validation",
                "Status": "⏳ Not Started",
                "Priority": "📋 Medium",
                "Category": "🔒 Security",
                "Notes": "Strengthen input sanitization and configuration validation"
            },
            {
                "Task": "Create production deployment guide",
                "Status": "⏳ Not Started",
                "Priority": "📋 Medium",
                "Category": "📚 Documentation",
                "Notes": "Step-by-step production deployment and maintenance procedures"
            }
        ]
        
        for task_data in tasks:
            page_data = {
                "parent": {"database_id": db_id},
                "properties": {
                    "Task": {
                        "title": [{"type": "text", "text": {"content": task_data["Task"]}}]
                    },
                    "Status": {
                        "select": {"name": task_data["Status"]}
                    },
                    "Priority": {
                        "select": {"name": task_data["Priority"]}
                    },
                    "Category": {
                        "select": {"name": task_data["Category"]}
                    },
                    "Notes": {
                        "rich_text": [{"type": "text", "text": {"content": task_data["Notes"]}}]
                    }
                }
            }
            
            response = requests.post('https://api.notion.com/v1/pages', 
                                   headers=self.headers, json=page_data)
            
            if response.status_code == 200:
                print(f"✅ Added task: {task_data['Task']}")
            else:
                print(f"❌ Failed to add task: {task_data['Task']}")
    
    def populate_configuration_db(self, db_id):
        """Populate configuration database"""
        print("🗃️ Populating Configuration Database...")
        
        configs = [
            {
                "Configuration Item": "Mobile Integration Settings",
                "Type": "📱 Mobile Config",
                "Value": "enable_mobile_integration: true, enable_pwa_features: true",
                "Description": "Controls mobile optimization and PWA features",
                "Status": "✅ Active"
            },
            {
                "Configuration Item": "Gamification System",
                "Type": "🎮 Gamification",
                "Value": "enable_gamification: true, achievement_system: enabled",
                "Description": "Achievement system, progress tracking, and motivational features",
                "Status": "✅ Active"
            },
            {
                "Configuration Item": "Advanced Notifications",
                "Type": "🔔 Notifications",
                "Value": "enable_advanced_notifications: true, smart_timing: enabled",
                "Description": "Smart timing, multi-channel delivery, and personalization",
                "Status": "✅ Active"
            },
            {
                "Configuration Item": "Multi-Model AI Support",
                "Type": "🤖 AI Settings",
                "Value": "enable_multi_model_ai: false, primary_model: gemini",
                "Description": "Support for Claude 3.5 Sonnet and GPT-4V alongside Gemini",
                "Status": "🔄 Testing"
            },
            {
                "Configuration Item": "Predictive Analytics",
                "Type": "🤖 AI Settings",
                "Value": "enable_predictive_analytics: true",
                "Description": "Historical pattern analysis and cleaning recommendations",
                "Status": "✅ Active"
            }
        ]
        
        for config_data in configs:
            page_data = {
                "parent": {"database_id": db_id},
                "properties": {
                    "Configuration Item": {
                        "title": [{"type": "text", "text": {"content": config_data["Configuration Item"]}}]
                    },
                    "Type": {
                        "select": {"name": config_data["Type"]}
                    },
                    "Value": {
                        "rich_text": [{"type": "text", "text": {"content": config_data["Value"]}}]
                    },
                    "Description": {
                        "rich_text": [{"type": "text", "text": {"content": config_data["Description"]}}]
                    },
                    "Status": {
                        "select": {"name": config_data["Status"]}
                    },
                    "Last Updated": {
                        "date": {"start": datetime.now(timezone.utc).isoformat()}
                    }
                }
            }
            
            response = requests.post('https://api.notion.com/v1/pages', 
                                   headers=self.headers, json=page_data)
            
            if response.status_code == 200:
                print(f"✅ Added config: {config_data['Configuration Item']}")
            else:
                print(f"❌ Failed to add config: {config_data['Configuration Item']}")
    
    def populate_documentation_db(self, db_id):
        """Populate documentation database"""
        print("📚 Populating Documentation Database...")
        
        docs = [
            {
                "Document Title": "Phase 3 Completion Summary",
                "Type": "📋 Changelog",
                "Status": "✅ Complete",
                "Priority": "⚡ High",
                "Description": "Complete summary of Phase 3 achievements and features",
                "File Path": "docs/PHASE3_COMPLETION_SUMMARY.md"
            },
            {
                "Document Title": "Test Status Analysis",
                "Type": "🔧 Technical Docs",
                "Status": "✅ Complete",
                "Priority": "⚡ High",
                "Description": "Comprehensive test analysis and production readiness assessment",
                "File Path": "docs/TEST_STATUS_ANALYSIS.md"
            },
            {
                "Document Title": "Next Agent Prompt V4",
                "Type": "📖 User Guide",
                "Status": "✅ Complete",
                "Priority": "⚡ High",
                "Description": "Comprehensive mission briefing for Phase 4 continuation",
                "File Path": "NEXT_AGENT_PROMPT_V4.md"
            },
            {
                "Document Title": "Project Handoff Summary",
                "Type": "📋 Changelog",
                "Status": "✅ Complete",
                "Priority": "⚡ High",
                "Description": "Complete project status and handoff information",
                "File Path": "PROJECT_HANDOFF_SUMMARY.md"
            },
            {
                "Document Title": "HA Add-on Store README",
                "Type": "📖 User Guide",
                "Status": "📝 Draft",
                "Priority": "⚡ High",
                "Description": "README.md for Home Assistant Add-on Store submission",
                "File Path": "README.md"
            },
            {
                "Document Title": "Installation and Configuration Guide",
                "Type": "📖 User Guide",
                "Status": "🔄 In Progress",
                "Priority": "⚡ High",
                "Description": "Comprehensive user installation and configuration guide",
                "File Path": "DOCS.md"
            },
            {
                "Document Title": "API Reference Documentation",
                "Type": "🚀 API Reference",
                "Status": "🔍 Review",
                "Priority": "📋 Medium",
                "Description": "Complete API documentation for all endpoints and features",
                "File Path": "docs/API_REFERENCE.md"
            }
        ]
        
        for doc_data in docs:
            page_data = {
                "parent": {"database_id": db_id},
                "properties": {
                    "Document Title": {
                        "title": [{"type": "text", "text": {"content": doc_data["Document Title"]}}]
                    },
                    "Type": {
                        "select": {"name": doc_data["Type"]}
                    },
                    "Status": {
                        "select": {"name": doc_data["Status"]}
                    },
                    "Priority": {
                        "select": {"name": doc_data["Priority"]}
                    },
                    "Description": {
                        "rich_text": [{"type": "text", "text": {"content": doc_data["Description"]}}]
                    },
                    "File Path": {
                        "rich_text": [{"type": "text", "text": {"content": doc_data["File Path"]}}]
                    },
                    "Last Updated": {
                        "date": {"start": datetime.now(timezone.utc).isoformat()}
                    }
                }
            }
            
            response = requests.post('https://api.notion.com/v1/pages', 
                                   headers=self.headers, json=page_data)
            
            if response.status_code == 200:
                print(f"✅ Added doc: {doc_data['Document Title']}")
            else:
                print(f"❌ Failed to add doc: {doc_data['Document Title']}")
    
    def populate_all_databases(self):
        """Populate all databases with initial content"""
        print("🗃️ POPULATING ALL DATABASES")
        print("=" * 50)
        
        # Get database IDs
        db_ids = self.get_database_ids()
        if not db_ids:
            print("❌ Could not find database IDs")
            return False
        
        print(f"Found databases: {list(db_ids.keys())}")
        
        # Populate each database
        if 'tasks' in db_ids:
            self.populate_phase4_tasks(db_ids['tasks'])
        
        if 'config' in db_ids:
            self.populate_configuration_db(db_ids['config'])
        
        if 'docs' in db_ids:
            self.populate_documentation_db(db_ids['docs'])
        
        print("\n🎉 ALL DATABASES POPULATED!")
        print("=" * 50)
        print("✅ Phase 4 Tasks Database - 10 actionable tasks")
        print("✅ Configuration Database - 5 key configurations")
        print("✅ Documentation Database - 7 important documents")
        print("✅ Bug Tracker Database - Ready for issue tracking")
        
        return True

def main():
    """Main execution function"""
    populator = NotionDatabasePopulator()
    success = populator.populate_all_databases()
    
    if success:
        print("\n🚀 Notion workspace is now fully organized and populated!")
        print("The incoming agent has everything needed for immediate Phase 4 work.")
    else:
        print("\n❌ Database population failed")

if __name__ == "__main__":
    main()
