#!/usr/bin/env python3
"""
Simple Notion API Integration for Augment Agent
"""

import os
import sys
import json
import requests

def load_env_vars():
    """Load environment variables from config files"""
    for env_file in ['.env.secrets', '.env.mcp']:
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#') and '=' in line:
                        key, value = line.strip().split('=', 1)
                        if key.startswith('export '):
                            key = key[7:]
                        # Remove quotes
                        value = value.strip('"').strip("'")
                        os.environ[key] = value

def test_notion_connection():
    """Test Notion API connection"""
    load_env_vars()
    
    token = os.getenv('NOTION_TOKEN')
    page_id = os.getenv('NOTION_PAGE_ID')
    
    if not token:
        print("❌ NOTION_TOKEN not found")
        return False
    if not page_id:
        print("❌ NOTION_PAGE_ID not found")
        return False
    
    print(f"✅ Token: {token[:10]}...")
    print(f"✅ Page ID: {page_id}")
    
    # Test API call
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "Notion-Version": "2022-06-28"
    }
    
    blocks = [{
        "object": "block",
        "type": "callout",
        "callout": {
            "rich_text": [{"type": "text", "text": {"content": "✅ Notion API Test Successful!"}}],
            "icon": {"type": "emoji", "emoji": "🧪"},
            "color": "default"
        }
    }]
    
    try:
        response = requests.patch(url, headers=headers, json={"children": blocks}, timeout=30)
        response.raise_for_status()
        print("✅ Successfully updated Notion workspace!")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_notion_connection()
    sys.exit(0 if success else 1)
