#!/usr/bin/env python3
"""
Script to automatically fix test configuration issues by applying TestConfigHelper pattern
"""

import os
import re
import sys

def fix_aicleaner_instantiation(content):
    """
    Fix AICleaner() instantiation to use TestConfigHelper pattern
    """
    
    # Pattern to match AICleaner() instantiation without proper config
    pattern = r'(\s+)cleaner = AICleaner\(\)'
    
    # Replacement with TestConfigHelper pattern
    replacement = r'''\1with TestConfigHelper() as helper:
\1    config_data = get_valid_aicleaner_config()
\1    env_patch = helper.patch_local_environment(config_data)
\1    
\1    with env_patch:
\1        with patch.object(aicleaner_module, 'HAClient') as mock_ha_client:
\1            with patch.object(aicleaner_module, 'GenerativeModel') as mock_gemini_client:
\1                mock_ha_client.return_value = helper.create_mock_ha_client()
\1                mock_gemini_client.return_value = helper.create_mock_gemini_client()
\1                
\1                cleaner = AICleaner()'''
    
    # Apply the replacement
    fixed_content = re.sub(pattern, replacement, content)
    
    return fixed_content

def fix_test_file(file_path):
    """
    Fix a single test file
    """
    print(f"Fixing {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check if file already has TestConfigHelper import
    if 'from fixtures.test_config_helper import TestConfigHelper' not in content:
        print(f"  Skipping {file_path} - no TestConfigHelper import found")
        return False
    
    # Check if file has failing AICleaner() instantiations
    if 'cleaner = AICleaner()' not in content:
        print(f"  Skipping {file_path} - no AICleaner instantiation found")
        return False
    
    # Apply fixes
    fixed_content = fix_aicleaner_instantiation(content)
    
    # Check if any changes were made
    if fixed_content == content:
        print(f"  No changes needed for {file_path}")
        return False
    
    # Write back the fixed content
    with open(file_path, 'w') as f:
        f.write(fixed_content)
    
    print(f"  Fixed {file_path}")
    return True

def main():
    """
    Main function to fix test configuration issues
    """
    test_files = [
        'tests/unit/test_aicleaner.py',
        'tests/unit/test_zone.py',
        'tests/test_api_endpoint_fix.py',
        'tests/test_camera_url_fix.py',
        'tests/test_configuration_manager.py',
        'tests/test_ha_cli_auth.py',
        'tests/test_lovelace_card_loading.py',
        'tests/test_service_integration.py',
        'tests/test_card_installation_verification.py'
    ]
    
    fixed_count = 0
    
    for file_path in test_files:
        if os.path.exists(file_path):
            if fix_test_file(file_path):
                fixed_count += 1
        else:
            print(f"Warning: {file_path} not found")
    
    print(f"\nFixed {fixed_count} test files")

if __name__ == '__main__':
    main()
