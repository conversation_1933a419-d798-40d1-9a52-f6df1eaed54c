# AICleaner v2.0 Template Sensors Fix
# 
# CRITICAL: Add these template sensors to your configuration.yaml to make AICleaner entities persistent
# This fixes the issue where the Lovelace card loads but shows "No zones configured"
#
# Background: AICleaner addon uses /api/states endpoint which creates temporary state dictionaries,
# not real Home Assistant entities. These template sensors create real, persistent entities.

template:
  # Global System Status Sensor
  - trigger:
      platform: state
      entity_id: sensor.aicleaner_system_status
    sensor:
      - name: "AICleaner System Status"
        unique_id: aicleaner_system_status_persistent
        state: "{{ trigger.to_state.state if trigger.to_state else 'inactive' }}"
        icon: "mdi:robot-vacuum"
        attributes:
          total_zones: "{{ trigger.to_state.attributes.total_zones if trigger.to_state else 0 }}"
          active_tasks: "{{ trigger.to_state.attributes.active_tasks if trigger.to_state else 0 }}"
          completed_tasks: "{{ trigger.to_state.attributes.completed_tasks if trigger.to_state else 0 }}"
          completion_rate: "{{ trigger.to_state.attributes.completion_rate if trigger.to_state else 0 }}"
          last_analysis: "{{ trigger.to_state.attributes.last_analysis if trigger.to_state else 'never' }}"
          version: "{{ trigger.to_state.attributes.version if trigger.to_state else '2.0' }}"
          zones: "{{ trigger.to_state.attributes.zones if trigger.to_state else [] }}"

  # Kitchen Zone Sensor (example - add similar for each zone)
  - trigger:
      platform: state
      entity_id: sensor.aicleaner_kitchen_tasks
    sensor:
      - name: "AICleaner Kitchen Tasks"
        unique_id: aicleaner_kitchen_tasks_persistent
        state: "{{ trigger.to_state.state if trigger.to_state else 'unknown' }}"
        icon: "mdi:chef-hat"
        attributes:
          zone_name: "{{ trigger.to_state.attributes.zone_name if trigger.to_state else 'kitchen' }}"
          active_tasks: "{{ trigger.to_state.attributes.active_tasks if trigger.to_state else [] }}"
          completed_tasks: "{{ trigger.to_state.attributes.completed_tasks if trigger.to_state else [] }}"
          completion_rate: "{{ trigger.to_state.attributes.completion_rate if trigger.to_state else 0 }}"
          last_analysis: "{{ trigger.to_state.attributes.last_analysis if trigger.to_state else 'never' }}"
          cleanliness_score: "{{ trigger.to_state.attributes.cleanliness_score if trigger.to_state else 0 }}"
          cleanliness_state: "{{ trigger.to_state.attributes.cleanliness_state if trigger.to_state else 'unknown' }}"
          analysis_count: "{{ trigger.to_state.attributes.analysis_count if trigger.to_state else 0 }}"

# INSTRUCTIONS:
# 1. Copy the above template configuration to your configuration.yaml file
# 2. Add similar template sensors for each zone you have configured in AICleaner
# 3. Restart Home Assistant
# 4. The Lovelace card should now show your zones and data
#
# For additional zones, copy the kitchen template and change:
# - entity_id: sensor.aicleaner_[ZONE_NAME]_tasks
# - name: "AICleaner [Zone Name] Tasks"  
# - unique_id: aicleaner_[zone_name]_tasks_persistent
# - zone_name default value
# - icon (optional)

# Example for Living Room zone:
# - trigger:
#     platform: state
#     entity_id: sensor.aicleaner_living_room_tasks
#   sensor:
#     - name: "AICleaner Living Room Tasks"
#       unique_id: aicleaner_living_room_tasks_persistent
#       state: "{{ trigger.to_state.state if trigger.to_state else 'unknown' }}"
#       icon: "mdi:sofa"
#       attributes:
#         zone_name: "{{ trigger.to_state.attributes.zone_name if trigger.to_state else 'living_room' }}"
#         active_tasks: "{{ trigger.to_state.attributes.active_tasks if trigger.to_state else [] }}"
#         completed_tasks: "{{ trigger.to_state.attributes.completed_tasks if trigger.to_state else [] }}"
#         completion_rate: "{{ trigger.to_state.attributes.completion_rate if trigger.to_state else 0 }}"
#         last_analysis: "{{ trigger.to_state.attributes.last_analysis if trigger.to_state else 'never' }}"
#         cleanliness_score: "{{ trigger.to_state.attributes.cleanliness_score if trigger.to_state else 0 }}"
#         cleanliness_state: "{{ trigger.to_state.attributes.cleanliness_state if trigger.to_state else 'unknown' }}"
#         analysis_count: "{{ trigger.to_state.attributes.analysis_count if trigger.to_state else 0 }}"
