#!/usr/bin/env python3
"""
Test script to verify the to-do list integration fix.

This script tests that the AICleaner sensor now properly reads from
both its internal state AND the Home Assistant to-do list entity.
"""

import sys
import os
import json
from unittest.mock import Mock, patch

# Add the aicleaner directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aicleaner'))

def test_todo_integration_fix():
    """Test that sensor data includes tasks from both sources."""
    
    print("🧪 Testing To-Do List Integration Fix")
    print("=" * 50)
    
    # Mock the HAClient to simulate Home Assistant to-do list data
    mock_ha_client = Mock()
    
    # Simulate the Home Assistant to-do list with 5 active tasks and 1 completed
    mock_ha_client.get_todo_list_items.return_value = [
        {
            'summary': 'Test task from AICleaner API test',
            'status': 'needs_action',
            'uid': 'task_1',
            'description': ''
        },
        {
            'summary': 'AICleaner Integration Test - 09:38:32',
            'status': 'needs_action',
            'uid': 'task_2',
            'description': ''
        },
        {
            'summary': '[E2E Test] Put away toys outside the crib (Priority: 9)',
            'status': 'needs_action',
            'uid': 'task_3',
            'description': ''
        },
        {
            'summary': '[E2E Test] Tidy crib contents (Priority: 8)',
            'status': 'needs_action',
            'uid': 'task_4',
            'description': ''
        },
        {
            'summary': 'Lovelace Card Test - 09:41:26',
            'status': 'needs_action',
            'uid': 'task_5',
            'description': ''
        },
        {
            'summary': 'Test task from AICleaner notification system - please complete',
            'status': 'completed',
            'uid': 'task_6',
            'description': ''
        }
    ]
    
    # Import and create a Zone instance with mocked dependencies
    try:
        from aicleaner import Zone
        
        # Create zone configuration
        zone_config = {
            'name': "Rowan's Room",
            'camera_entity': 'camera.rowan_room_fluent',
            'todo_list_entity': 'todo.rowan_s_room_tasks',
            'purpose': 'Keep the kitchen clean and organized',
            'update_frequency': 30,
            'notifications_enabled': True,
            'notification_service': 'mobile_app_drew_iphone'
        }
        
        # Create zone state with some internal tasks
        zone_state = {
            'tasks': [
                {
                    'id': 'internal_task_1',
                    'description': 'Internal AICleaner task',
                    'status': 'active',
                    'priority': 7,
                    'created_at': '2025-07-03T22:00:00Z'
                },
                {
                    'id': 'internal_task_2',
                    'description': 'Completed internal task',
                    'status': 'completed',
                    'priority': 5,
                    'created_at': '2025-07-03T21:00:00Z',
                    'completed_at': '2025-07-03T21:30:00Z'
                }
            ]
        }
        
        # Mock other dependencies
        mock_gemini_client = Mock()
        
        # Create Zone instance
        zone = Zone(zone_config, zone_state, mock_ha_client, mock_gemini_client)
        
        # Test the sensor data
        sensor_data = zone.get_sensor_data()
        
        print("📊 Sensor Data Results:")
        print(f"   State (active tasks): {sensor_data['state']}")
        print(f"   Total tasks: {sensor_data['attributes']['total_tasks']}")
        print(f"   Active tasks: {sensor_data['attributes']['active_tasks']}")
        print(f"   Completed tasks: {sensor_data['attributes']['completed_tasks']}")
        print(f"   Completion rate: {sensor_data['attributes']['completion_rate']}%")
        
        # Expected results:
        # - 5 active tasks from HA to-do list + 1 internal active = 6 active tasks
        # - 1 completed task from HA to-do list + 1 internal completed = 2 completed tasks
        # - Total: 8 tasks
        # - Completion rate: 2/8 = 25%
        
        expected_active = 6
        expected_completed = 2
        expected_total = 8
        expected_completion_rate = 25.0
        
        # Verify results
        success = True
        
        if sensor_data['state'] != expected_active:
            print(f"❌ FAIL: Expected {expected_active} active tasks, got {sensor_data['state']}")
            success = False
        else:
            print(f"✅ PASS: Active tasks count correct ({expected_active})")
        
        if sensor_data['attributes']['active_tasks'] != expected_active:
            print(f"❌ FAIL: Expected {expected_active} active tasks in attributes, got {sensor_data['attributes']['active_tasks']}")
            success = False
        else:
            print(f"✅ PASS: Active tasks attribute correct ({expected_active})")
        
        if sensor_data['attributes']['completed_tasks'] != expected_completed:
            print(f"❌ FAIL: Expected {expected_completed} completed tasks, got {sensor_data['attributes']['completed_tasks']}")
            success = False
        else:
            print(f"✅ PASS: Completed tasks count correct ({expected_completed})")
        
        if sensor_data['attributes']['total_tasks'] != expected_total:
            print(f"❌ FAIL: Expected {expected_total} total tasks, got {sensor_data['attributes']['total_tasks']}")
            success = False
        else:
            print(f"✅ PASS: Total tasks count correct ({expected_total})")
        
        if sensor_data['attributes']['completion_rate'] != expected_completion_rate:
            print(f"❌ FAIL: Expected {expected_completion_rate}% completion rate, got {sensor_data['attributes']['completion_rate']}%")
            success = False
        else:
            print(f"✅ PASS: Completion rate correct ({expected_completion_rate}%)")
        
        # Check task details
        task_details = sensor_data['attributes']['tasks']
        print(f"\n📝 Task Details ({len(task_details)} tasks):")
        for i, task in enumerate(task_details):
            source = task.get('source', 'unknown')
            print(f"   {i+1}. {task['description']} (source: {source})")
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 All tests PASSED! The to-do list integration fix is working correctly.")
            print("\n📋 Next Steps:")
            print("1. Restart the AICleaner addon")
            print("2. Check that the Lovelace card now shows the correct task count")
            print("3. Verify task details are displayed properly")
        else:
            print("❌ Some tests FAILED. Please review the integration logic.")
        
        return success
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    test_todo_integration_fix()
