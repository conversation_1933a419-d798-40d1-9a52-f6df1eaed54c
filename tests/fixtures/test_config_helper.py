"""
Test Configuration Helper - Provides proper test configurations for AICleaner tests
Following TDD + AAA testing principles
"""

import os
import json
import tempfile
from typing import Dict, Any
from unittest.mock import patch, MagicMock
from tests.fixtures.test_configs import get_valid_aicleaner_config


class ConfigHelper:
    """Helper class for managing test configurations in AICleaner tests"""
    
    def __init__(self):
        self.temp_files = []
        self.patches = []
    
    def setup_test_environment(self):
        """Set up test environment variables"""
        os.environ['TESTING'] = 'true'
        return self
    
    def create_mock_addon_options_file(self, config: Dict[str, Any] = None) -> str:
        """
        Create a temporary addon options file for testing
        
        Args:
            config: Configuration to write to options file
            
        Returns:
            Path to the temporary options file
        """
        if config is None:
            config = get_valid_aicleaner_config()
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(config, temp_file, indent=2)
        temp_file.close()
        
        self.temp_files.append(temp_file.name)
        return temp_file.name
    
    def patch_addon_environment(self, config: Dict[str, Any] = None):
        """
        Patch the addon environment for testing
        
        Args:
            config: Configuration to use for the addon
            
        Returns:
            Context manager for the patch
        """
        if config is None:
            config = get_valid_aicleaner_config()
        
        # Create mock options file
        options_file = self.create_mock_addon_options_file(config)
        
        # Set up environment variables
        env_patches = {
            'SUPERVISOR_TOKEN': 'test_supervisor_token',
            'HA_TOKEN': 'test_ha_token'
        }
        
        # Patch os.path.exists to return True for our mock options file
        def mock_exists(path):
            if path == '/data/options.json':
                return True
            return os.path.exists(path)
        
        # Patch open to return our mock file content
        def mock_open(path, mode='r'):
            if path == '/data/options.json':
                return open(options_file, mode)
            return open(path, mode)
        
        # Create patches
        env_patch = patch.dict(os.environ, env_patches)
        exists_patch = patch('os.path.exists', side_effect=mock_exists)
        open_patch = patch('builtins.open', side_effect=mock_open)
        
        # Store patches for cleanup
        self.patches.extend([env_patch, exists_patch, open_patch])
        
        return env_patch, exists_patch, open_patch
    
    def patch_local_environment(self, config: Dict[str, Any] = None):
        """
        Patch the local development environment for testing
        
        Args:
            config: Configuration to use
            
        Returns:
            Context manager for the patch
        """
        if config is None:
            config = get_valid_aicleaner_config()
        
        # Remove SUPERVISOR_TOKEN to trigger local environment
        env_patches = {
            'GEMINI_API_KEY': config.get('gemini_api_key', 'test_api_key'),
            'DISPLAY_NAME': config.get('display_name', 'Test User'),
            'HA_API_URL': config.get('ha_api_url', 'http://localhost:8123/api'),
            'HA_TOKEN': config.get('ha_token', 'test_ha_token'),
            'ZONES_COUNT': str(len(config.get('zones', [])))
        }
        
        # Add zone-specific environment variables
        for i, zone in enumerate(config.get('zones', [])):
            prefix = f'ZONE_{i}_'
            env_patches.update({
                f'{prefix}NAME': zone.get('name', f'Zone {i}'),
                f'{prefix}ICON': zone.get('icon', 'mdi:home'),
                f'{prefix}PURPOSE': zone.get('purpose', 'Keep clean'),
                f'{prefix}CAMERA_ENTITY': zone.get('camera_entity', f'camera.zone_{i}'),
                f'{prefix}TODO_LIST_ENTITY': zone.get('todo_list_entity', f'todo.zone_{i}_tasks'),
                f'{prefix}UPDATE_FREQUENCY': str(zone.get('update_frequency', 24)),
                f'{prefix}NOTIFICATIONS_ENABLED': str(zone.get('notifications_enabled', True)).lower(),
                f'{prefix}NOTIFICATION_SERVICE': zone.get('notification_service', 'notify.mobile_app'),
                f'{prefix}NOTIFICATION_PERSONALITY': zone.get('notification_personality', 'default'),
                f'{prefix}NOTIFY_ON_CREATE': str(zone.get('notify_on_create', True)).lower(),
                f'{prefix}NOTIFY_ON_COMPLETE': str(zone.get('notify_on_complete', True)).lower()
            })
        
        # Remove SUPERVISOR_TOKEN if it exists
        if 'SUPERVISOR_TOKEN' in os.environ:
            del os.environ['SUPERVISOR_TOKEN']
        
        env_patch = patch.dict(os.environ, env_patches)
        self.patches.append(env_patch)
        
        return env_patch
    
    def create_mock_ha_client(self):
        """Create a mock HA client for testing"""
        mock_client = MagicMock()
        mock_client.call_count = MagicMock(return_value=0)
        mock_client.get_camera_snapshot.return_value = '/tmp/test_snapshot.jpg'
        mock_client.add_todo_item.return_value = True
        mock_client.update_todo_item.return_value = True
        mock_client.send_notification.return_value = True
        mock_client.update_sensor.return_value = True
        return mock_client
    
    def create_mock_gemini_client(self):
        """Create a mock Gemini client for testing"""
        mock_client = MagicMock()
        mock_client.generate_content.return_value.text = json.dumps({
            "new_tasks": [
                {
                    "description": "Test task from AI analysis",
                    "priority": 5,
                    "category": "cleaning"
                }
            ],
            "completed_tasks": []
        })
        return mock_client
    
    def cleanup(self):
        """Clean up temporary files and patches"""
        # Stop all patches
        for patch_obj in self.patches:
            try:
                patch_obj.stop()
            except:
                pass
        self.patches.clear()
        
        # Remove temporary files
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass
        self.temp_files.clear()
        
        # Clean up environment
        if 'TESTING' in os.environ:
            del os.environ['TESTING']
    
    def __enter__(self):
        """Context manager entry"""
        return self.setup_test_environment()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()


# Convenience functions for common test scenarios

def with_test_config(config: Dict[str, Any] = None):
    """
    Decorator to provide test configuration to test methods
    
    Usage:
        @with_test_config()
        def test_something(self):
            # Test code here
    """
    def decorator(test_func):
        def wrapper(*args, **kwargs):
            with TestConfigHelper() as helper:
                if config:
                    env_patch = helper.patch_local_environment(config)
                    with env_patch:
                        return test_func(*args, **kwargs)
                else:
                    return test_func(*args, **kwargs)
        return wrapper
    return decorator


def create_test_aicleaner_instance():
    """
    Create an AICleaner instance with proper test configuration
    
    Returns:
        Configured AICleaner instance for testing
    """
    from aicleaner import AICleaner
    
    helper = TestConfigHelper()
    config = get_valid_aicleaner_config()
    
    # Patch environment
    env_patch = helper.patch_local_environment(config)
    
    with env_patch:
        # Mock the API clients to avoid real API calls
        with patch('aicleaner.aicleaner.HAClient') as mock_ha_client, \
             patch('aicleaner.aicleaner.GenerativeModel') as mock_gemini_client:
            
            mock_ha_client.return_value = helper.create_mock_ha_client()
            mock_gemini_client.return_value = helper.create_mock_gemini_client()
            
            return AICleaner()
