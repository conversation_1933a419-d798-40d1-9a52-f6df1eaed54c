<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal AICleaner Card Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            --primary-color: #2196F3;
            --text-primary-color: white;
            --card-background-color: white;
            --secondary-background-color: #f8f9fa;
            --primary-text-color: #212121;
            --secondary-text-color: #757575;
            --divider-color: #e0e0e0;
        }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        
        .card-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: white;
        }
    </style>
</head>
<body>
    <h1>🧪 Minimal AICleaner Card Test</h1>
    
    <div id="test-results">
        <div class="test-result">⏳ Running tests...</div>
    </div>
    
    <div class="card-container">
        <h3>Card Preview:</h3>
        <div id="card-preview"></div>
    </div>

    <!-- Load the card -->
    <script src="aicleaner-card.js"></script>
    
    <script>
        console.log('🚀 Starting minimal test...');
        
        // Simple test results array
        const results = [];
        
        function addResult(name, passed, error = null) {
            results.push({ name, passed, error });
            console.log(`${passed ? '✅' : '❌'} ${name}${error ? ': ' + error : ''}`);
        }
        
        function displayResults() {
            const container = document.getElementById('test-results');
            const passed = results.filter(r => r.passed).length;
            const total = results.length;
            
            container.innerHTML = `
                <div class="test-result ${passed === total ? 'pass' : 'fail'}">
                    Tests: ${passed}/${total} passed
                </div>
                ${results.map(result => `
                    <div class="test-result ${result.passed ? 'pass' : 'fail'}">
                        ${result.passed ? '✅' : '❌'} ${result.name}
                        ${result.error ? `<br><small>${result.error}</small>` : ''}
                    </div>
                `).join('')}
            `;
        }
        
        // Run tests
        async function runMinimalTests() {
            console.log('🔴 RED PHASE: Testing basic functionality...');
            
            // Test 1: Custom element registration
            try {
                const isRegistered = customElements.get('aicleaner-card') !== undefined;
                addResult('Custom element registration', isRegistered);
            } catch (error) {
                addResult('Custom element registration', false, error.message);
            }
            
            // Test 2: Element creation
            let card = null;
            try {
                card = document.createElement('aicleaner-card');
                const created = card instanceof HTMLElement;
                addResult('Element creation', created);
            } catch (error) {
                addResult('Element creation', false, error.message);
            }
            
            // Test 3: Configuration
            if (card) {
                try {
                    card.setConfig({ title: 'Test Card' });
                    const configured = card._config && card._config.title === 'Test Card';
                    addResult('Configuration', configured);
                } catch (error) {
                    addResult('Configuration', false, error.message);
                }
            }
            
            // Test 4: Shadow DOM
            if (card) {
                try {
                    const hasShadowRoot = card.shadowRoot !== null;
                    addResult('Shadow DOM creation', hasShadowRoot);
                } catch (error) {
                    addResult('Shadow DOM creation', false, error.message);
                }
            }
            
            // Test 5: Rendering
            if (card) {
                try {
                    document.getElementById('card-preview').appendChild(card);
                    
                    // Give it time to render
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    const hasContent = card.shadowRoot && card.shadowRoot.innerHTML.length > 0;
                    addResult('Basic rendering', hasContent);
                } catch (error) {
                    addResult('Basic rendering', false, error.message);
                }
            }
            
            // Test 6: Mock data processing
            if (card) {
                try {
                    const mockHass = {
                        states: {
                            'sensor.aicleaner_system_status': {
                                state: 'active',
                                attributes: {
                                    total_zones: 2,
                                    total_active_tasks: 5
                                }
                            }
                        }
                    };
                    
                    card.hass = mockHass;
                    
                    const dataProcessed = card.systemStatus && 
                                        card.systemStatus.totalZones === 2;
                    addResult('Data processing', dataProcessed);
                } catch (error) {
                    addResult('Data processing', false, error.message);
                }
            }
            
            displayResults();
            
            const passed = results.filter(r => r.passed).length;
            const total = results.length;
            
            if (passed === total) {
                console.log('🎉 All basic tests passed! Ready for advanced testing.');
            } else {
                console.log(`🔴 ${total - passed} tests failed. Implementing fixes...`);
            }
        }
        
        // Start tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            runMinimalTests();
        });
    </script>
</body>
</html>
