"""
Test suite for camera URL fix verification using TDD and AAA principles.

This module tests that the camera URL construction fix works correctly
and that the addon can successfully connect to camera endpoints.
"""

import pytest
import os
import json
import tempfile
from unittest.mock import patch, MagicMock, mock_open
import sys

# Add the parent directory to sys.path to import our components
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'aicleaner'))

from aicleaner import AICleaner, HAClient

# Import test fixtures
sys.path.append(os.path.join(os.path.dirname(__file__)))
from fixtures.test_config_helper import ConfigHelper
from fixtures.test_configs import get_valid_aicleaner_config


class TestCameraURLFix:
    """Test class for camera URL fix verification."""
    
    def test_ha_client_camera_url_construction(self):
        """
        Test that HAClient constructs camera URLs correctly.
        
        Arrange: HAClient with supervisor API URL
        Act: Construct camera snapshot URL
        Assert: URL has correct format without double /api
        """
        # Arrange
        api_url = "http://supervisor/core"
        token = "test_token"
        client = HAClient(api_url, token)
        entity_id = "camera.test_camera"
        
        # Act
        # We need to access the URL construction logic
        expected_url = f"{api_url}/api/camera_proxy/{entity_id}"
        
        # Assert
        assert expected_url == "http://supervisor/core/api/camera_proxy/camera.test_camera", \
            "Camera URL should be constructed correctly without double /api"
        assert "/api/api/" not in expected_url, "URL should not contain double /api"
    
    @patch('requests.get')
    def test_camera_snapshot_with_fixed_url(self, mock_get):
        """
        Test camera snapshot with the fixed URL construction.
        
        Arrange: Mock successful camera response with fixed URL
        Act: Get camera snapshot
        Assert: Correct URL is called and snapshot is saved
        """
        # Arrange
        mock_get.return_value.status_code = 200
        mock_get.return_value.content = b"fake_image_data"
        
        api_url = "http://supervisor/core"
        token = "test_token"
        client = HAClient(api_url, token)
        entity_id = "camera.rowan_room_fluent"
        
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            filename = tmp_file.name
        
        # Act
        result = client.get_camera_snapshot(entity_id, filename)
        
        # Assert
        assert result is True, "Camera snapshot should succeed"
        mock_get.assert_called_once_with(
            "http://supervisor/core/api/camera_proxy/camera.rowan_room_fluent",
            headers=client.headers,
            timeout=10
        )
        
        # Cleanup
        os.unlink(filename)
    
    def test_addon_environment_api_url_configuration(self):
        """
        Test that addon environment sets correct API URL.

        Arrange: Mock addon environment with options.json
        Act: Load configuration from addon environment
        Assert: API URL is set correctly without trailing /api
        """
        # Arrange
        with ConfigHelper() as helper:
            test_options = {
                "gemini_api_key": "test_key",
                "display_name": "Test User",
                "zones": [{
                    "name": "kitchen",
                    "icon": "mdi:chef-hat",
                    "purpose": "Test kitchen zone",
                    "camera_entity": "camera.test",
                    "todo_list_entity": "todo.test",
                    "update_frequency": 30,
                    "notifications_enabled": False,
                    "notification_service": "",
                    "notification_personality": "default",
                    "notify_on_create": False,
                    "notify_on_complete": False
                }]
            }

            # Mock environment - clear HA_TOKEN to force supervisor token usage
            env_vars = {'SUPERVISOR_TOKEN': 'test_supervisor_token'}
            if 'HA_TOKEN' in os.environ:
                env_vars['HA_TOKEN'] = ''
            with patch.dict(os.environ, env_vars, clear=False):
                with patch('os.path.exists', return_value=True):
                    with patch('builtins.open', mock_open(read_data=json.dumps(test_options))):
                        with patch('aicleaner.HAClient') as mock_ha_client:
                            with patch('aicleaner.GenerativeModel') as mock_gemini_client:
                                mock_ha_client.return_value = helper.create_mock_ha_client()
                                mock_gemini_client.return_value = helper.create_mock_gemini_client()

                                # Act
                                cleaner = AICleaner()
                                config = cleaner._load_from_addon_env()

                                # Assert
                                assert config['ha_api_url'] == 'http://supervisor/core', \
                                    "API URL should be set correctly without trailing /api"
                                assert not config['ha_api_url'].endswith('/api'), \
                                    "API URL should not end with /api"
    
    @patch('requests.get')
    def test_camera_snapshot_error_handling(self, mock_get):
        """
        Test camera snapshot error handling with proper URL.
        
        Arrange: Mock 404 error response (camera not found)
        Act: Attempt to get camera snapshot
        Assert: Error is handled gracefully and logged
        """
        # Arrange
        mock_get.return_value.status_code = 404
        mock_get.return_value.raise_for_status.side_effect = Exception("404 Client Error")
        
        api_url = "http://supervisor/core"
        token = "test_token"
        client = HAClient(api_url, token)
        entity_id = "camera.nonexistent"
        
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            filename = tmp_file.name
        
        # Act
        result = client.get_camera_snapshot(entity_id, filename)
        
        # Assert
        assert result is False, "Camera snapshot should fail gracefully"
        mock_get.assert_called_once_with(
            "http://supervisor/core/api/camera_proxy/camera.nonexistent",
            headers=client.headers,
            timeout=10
        )
        
        # Cleanup
        os.unlink(filename)


def mock_open_read_data(data):
    """Helper function to create mock_open with read data."""
    from unittest.mock import mock_open
    return mock_open(read_data=data)


class TestCameraURLIntegration:
    """Integration tests for camera URL fix."""
    
    def test_full_camera_workflow_with_fixed_url(self):
        """
        Test complete camera workflow with URL fix.
        
        Arrange: Complete AICleaner setup with test configuration
        Act: Attempt camera snapshot in zone analysis
        Assert: Correct URL is used and workflow completes
        """
        # This test would require more complex mocking
        # Skip for now but structure is ready for implementation
        pytest.skip("Integration test - requires full environment setup")
    
    def test_multiple_zones_camera_urls(self):
        """
        Test camera URL construction for multiple zones.
        
        Arrange: Configuration with multiple zones and cameras
        Act: Process each zone's camera
        Assert: All URLs are constructed correctly
        """
        pytest.skip("Multi-zone test - implement after single zone verification")
