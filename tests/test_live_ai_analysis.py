"""
Test Live AI Analysis functionality following TDD/AAA principles
"""
import pytest
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from PIL import Image

# Import test fixtures
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from fixtures.test_configs import get_valid_zone_config
from mocks.mock_ha_api import MockHAClient
from mocks.mock_gemini_api import MockGeminiClient

# Import the Zone class
from aicleaner import Zone


class TestLiveAIAnalysis:
    """Test suite for live AI analysis functionality"""

    def test_analyze_room_cleanliness_live_success(self):
        """
        AAA Test: Live cleanliness analysis should return comprehensive assessment
        """
        # Arrange
        zone_config = get_valid_zone_config()
        zone_state = {'tasks': []}
        ha_client = MockHAClient()
        gemini_client = MockGeminiClient()
        
        # Mock cleanliness response
        cleanliness_response = {
            "overall_score": 7.5,
            "detailed_scores": {
                "cleanliness": 8,
                "organization": 7,
                "surface_cleanliness": 8,
                "clutter_level": 6,
                "maintenance_needs": 9
            },
            "areas_needing_attention": ["countertop has items", "floor needs sweeping"],
            "positive_aspects": ["good lighting", "appliances are clean"],
            "immediate_actions": ["clear countertop", "sweep floor"],
            "condition_summary": "Generally clean with minor organization needed",
            "confidence_level": 0.85
        }
        
        gemini_client.add_response(cleanliness_response)
        zone = Zone(zone_config, zone_state, ha_client, gemini_client)
        
        # Create test image
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            test_image = Image.new('RGB', (100, 100), color='white')
            test_image.save(tmp_file.name)
            image_path = tmp_file.name
        
        try:
            # Act
            result = zone.analyze_room_cleanliness_live(image_path)
            
            # Assert
            assert result is not None
            assert result['overall_score'] == 7.5
            assert 'detailed_scores' in result
            assert 'areas_needing_attention' in result
            assert 'analysis_timestamp' in result
            assert result['zone_name'] == zone.name
            assert len(result['immediate_actions']) == 2
            
        finally:
            os.unlink(image_path)

    def test_live_analysis_integration_in_cycle(self):
        """
        AAA Test: Live analysis should be integrated into analysis cycle
        """
        # Arrange
        zone_config = get_valid_zone_config()
        zone_state = {'tasks': []}
        ha_client = MockHAClient()
        gemini_client = MockGeminiClient()
        
        # Mock responses
        gemini_client.add_completed_tasks_response([])  # No completed tasks
        gemini_client.add_new_tasks_response([])  # No new tasks
        
        # Mock cleanliness response
        cleanliness_response = {
            "overall_score": 8.0,
            "detailed_scores": {"cleanliness": 8, "organization": 8},
            "condition_summary": "Room is in good condition",
            "confidence_level": 0.9
        }
        gemini_client.add_response(cleanliness_response)
        
        zone = Zone(zone_config, zone_state, ha_client, gemini_client)
        
        # Mock PIL.Image.open and camera snapshot
        with patch('PIL.Image.open', return_value=MagicMock()):
            with patch.object(zone, 'get_camera_snapshot', return_value='/tmp/test.jpg'):
                # Act
                zone.run_analysis_cycle()
                
                # Assert
                assert 'live_analysis' in zone.state
                assert zone.state['live_analysis']['overall_score'] == 8.0
                assert ha_client.call_count('update_sensor') >= 1
                
                # Check cleanliness sensor was updated
                sensor_calls = ha_client.get_api_calls('update_sensor')
                cleanliness_sensor_call = None
                for call in sensor_calls:
                    if 'cleanliness' in call['entity_id']:
                        cleanliness_sensor_call = call
                        break

                assert cleanliness_sensor_call is not None
                assert cleanliness_sensor_call['state'] == 'Good'  # Score 8.0 = Good
                assert 'overall_score' in cleanliness_sensor_call['attributes']

    def test_cleanliness_state_labels(self):
        """
        AAA Test: Cleanliness scores should map to correct state labels
        """
        # Arrange
        zone_config = get_valid_zone_config()
        zone_state = {'tasks': []}
        ha_client = MockHAClient()
        gemini_client = MockGeminiClient()
        zone = Zone(zone_config, zone_state, ha_client, gemini_client)
        
        # Act & Assert
        assert zone._get_cleanliness_state_label(9.5) == "Excellent"
        assert zone._get_cleanliness_state_label(8.0) == "Good"
        assert zone._get_cleanliness_state_label(6.0) == "Fair"
        assert zone._get_cleanliness_state_label(4.0) == "Poor"
        assert zone._get_cleanliness_state_label(2.0) == "Needs Attention"

    def test_live_analysis_error_handling(self):
        """
        AAA Test: Live analysis should handle errors gracefully
        """
        # Arrange
        zone_config = get_valid_zone_config()
        zone_state = {'tasks': []}
        ha_client = MockHAClient()
        gemini_client = MockGeminiClient()
        
        # Mock invalid response
        gemini_client.add_malformed_response("Invalid JSON response")
        zone = Zone(zone_config, zone_state, ha_client, gemini_client)
        
        # Act
        result = zone.analyze_room_cleanliness_live('/nonexistent/path.jpg')
        
        # Assert
        assert result is None

    def test_cleanliness_response_parsing(self):
        """
        AAA Test: Cleanliness response parsing should validate required fields
        """
        # Arrange
        zone_config = get_valid_zone_config()
        zone_state = {'tasks': []}
        ha_client = MockHAClient()
        gemini_client = MockGeminiClient()
        zone = Zone(zone_config, zone_state, ha_client, gemini_client)
        
        # Act & Assert - Valid response
        valid_response = '{"overall_score": 7.5, "detailed_scores": {}, "condition_summary": "Good"}'
        result = zone._parse_cleanliness_response(valid_response)
        assert result is not None
        assert result['overall_score'] == 7.5
        
        # Act & Assert - Invalid response (missing required fields)
        invalid_response = '{"overall_score": 7.5}'
        result = zone._parse_cleanliness_response(invalid_response)
        assert result is None
        
        # Act & Assert - Invalid score range
        invalid_score_response = '{"overall_score": 15, "detailed_scores": {}, "condition_summary": "Good"}'
        result = zone._parse_cleanliness_response(invalid_score_response)
        assert result is None
