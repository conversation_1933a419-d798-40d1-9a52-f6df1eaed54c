# SENSITIVE INFORMATION TEMPLATE
# Copy this file to .env.secrets and fill in your actual API keys
# DO NOT commit .env.secrets to version control

# GitHub Personal Access Token
# Get from: https://github.com/settings/tokens
# Scopes needed: repo, user, gist
export GITHUB_PERSONAL_ACCESS_TOKEN="your_github_token_here"

# Brave Search API Key
# Get from: https://brave.com/search/api/
# Free tier: 2,000 queries/month
export BRAVE_API_KEY="your_brave_api_key_here"

# Notion MCP for project management and documentation
# Get your integration token from: https://www.notion.so/my-integrations
export NOTION_TOKEN="your_notion_token_here"

# Add other sensitive environment variables here as needed
# export OTHER_API_KEY="your_key_here"
# export DATABASE_PASSWORD="your_password_here"
# export HOME_ASSISTANT_TOKEN="your_ha_token_here"
