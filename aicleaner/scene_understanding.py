"""
Advanced Scene Understanding for AICleaner
Provides enhanced context awareness with room type detection, object recognition, and seasonal adjustments
"""
import os
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
import re


class RoomType(Enum):
    """Types of rooms with specific cleaning contexts"""
    KITCHEN = "kitchen"
    BATHROOM = "bathroom"
    BEDROOM = "bedroom"
    LIVING_ROOM = "living_room"
    DINING_ROOM = "dining_room"
    OFFICE = "office"
    LAUNDRY_ROOM = "laundry_room"
    GARAGE = "garage"
    BASEMENT = "basement"
    ATTIC = "attic"
    HALLWAY = "hallway"
    CLOSET = "closet"
    PANTRY = "pantry"
    UNKNOWN = "unknown"


class Season(Enum):
    """Seasons for seasonal adjustments"""
    SPRING = "spring"
    SUMMER = "summer"
    FALL = "fall"
    WINTER = "winter"


class TimeOfDay(Enum):
    """Time periods for context-aware analysis"""
    MORNING = "morning"
    AFTERNOON = "afternoon"
    EVENING = "evening"
    NIGHT = "night"


@dataclass
class SceneContext:
    """Represents the context of a scene for enhanced analysis"""
    room_type: RoomType
    detected_objects: List[str]
    lighting_condition: str
    time_of_day: TimeOfDay
    season: Season
    weather_context: Optional[str] = None
    occupancy_level: str = "unknown"  # "empty", "low", "medium", "high"
    cleanliness_indicators: List[str] = None
    
    def __post_init__(self):
        if self.cleanliness_indicators is None:
            self.cleanliness_indicators = []


@dataclass
class ContextualInsight:
    """Represents a contextual insight about the scene"""
    insight_type: str
    description: str
    confidence: float
    room_specific: bool
    seasonal_relevance: bool
    time_sensitive: bool
    supporting_evidence: List[str]


class AdvancedSceneUnderstanding:
    """
    Advanced scene understanding system for AICleaner
    
    Features:
    - Room type detection and classification
    - Object recognition and context analysis
    - Seasonal cleaning adjustments
    - Time-of-day context awareness
    - Occupancy and usage pattern detection
    - Context-aware task prioritization
    """
    
    def __init__(self, data_path: str = "/data/scene_context"):
        """
        Initialize advanced scene understanding system
        
        Args:
            data_path: Path to store scene context data
        """
        self.data_path = data_path
        self.logger = logging.getLogger(__name__)
        
        # Ensure data directory exists
        os.makedirs(data_path, exist_ok=True)
        
        # Load room type patterns and object databases
        self.room_patterns = self._load_room_patterns()
        self.object_database = self._load_object_database()
        self.seasonal_adjustments = self._load_seasonal_adjustments()
        
        self.logger.info("Advanced Scene Understanding system initialized")
    
    def _load_room_patterns(self) -> Dict[RoomType, Dict[str, List[str]]]:
        """Load room type detection patterns"""
        return {
            RoomType.KITCHEN: {
                'objects': ['stove', 'oven', 'refrigerator', 'sink', 'dishwasher', 'microwave', 
                           'counter', 'cabinet', 'cutting board', 'pot', 'pan', 'dishes'],
                'keywords': ['kitchen', 'cook', 'food', 'meal', 'dining', 'eat'],
                'surfaces': ['countertop', 'stovetop', 'backsplash', 'island']
            },
            RoomType.BATHROOM: {
                'objects': ['toilet', 'sink', 'shower', 'bathtub', 'mirror', 'towel', 
                           'toothbrush', 'soap', 'shampoo', 'toilet paper'],
                'keywords': ['bathroom', 'bath', 'shower', 'toilet', 'wash'],
                'surfaces': ['floor', 'walls', 'mirror', 'fixtures']
            },
            RoomType.BEDROOM: {
                'objects': ['bed', 'pillow', 'blanket', 'dresser', 'nightstand', 'lamp', 
                           'closet', 'clothes', 'mattress'],
                'keywords': ['bedroom', 'sleep', 'bed', 'rest', 'night'],
                'surfaces': ['floor', 'bed', 'furniture']
            },
            RoomType.LIVING_ROOM: {
                'objects': ['sofa', 'couch', 'tv', 'coffee table', 'chair', 'cushion', 
                           'remote', 'book', 'magazine', 'lamp'],
                'keywords': ['living', 'family', 'lounge', 'relax', 'tv'],
                'surfaces': ['floor', 'furniture', 'tables']
            },
            RoomType.OFFICE: {
                'objects': ['desk', 'computer', 'chair', 'monitor', 'keyboard', 'mouse', 
                           'papers', 'books', 'printer', 'phone'],
                'keywords': ['office', 'work', 'study', 'desk', 'computer'],
                'surfaces': ['desk', 'floor', 'shelves']
            },
            RoomType.LAUNDRY_ROOM: {
                'objects': ['washer', 'dryer', 'laundry basket', 'detergent', 'clothes', 
                           'iron', 'ironing board'],
                'keywords': ['laundry', 'wash', 'dry', 'clean clothes'],
                'surfaces': ['floor', 'machines', 'folding area']
            }
        }
    
    def _load_object_database(self) -> Dict[str, Dict[str, Any]]:
        """Load object database with cleaning context"""
        return {
            'kitchen_appliances': {
                'objects': ['stove', 'oven', 'refrigerator', 'dishwasher', 'microwave'],
                'cleaning_frequency': 'weekly',
                'priority_level': 'high',
                'seasonal_factors': ['holiday_cooking', 'summer_usage']
            },
            'bathroom_fixtures': {
                'objects': ['toilet', 'sink', 'shower', 'bathtub'],
                'cleaning_frequency': 'daily',
                'priority_level': 'high',
                'seasonal_factors': ['humidity', 'mold_risk']
            },
            'furniture': {
                'objects': ['sofa', 'chair', 'table', 'desk', 'bed'],
                'cleaning_frequency': 'weekly',
                'priority_level': 'medium',
                'seasonal_factors': ['dust_accumulation', 'pet_hair']
            },
            'electronics': {
                'objects': ['tv', 'computer', 'monitor', 'phone'],
                'cleaning_frequency': 'weekly',
                'priority_level': 'low',
                'seasonal_factors': ['dust', 'static']
            }
        }
    
    def _load_seasonal_adjustments(self) -> Dict[Season, Dict[str, Any]]:
        """Load seasonal cleaning adjustments"""
        return {
            Season.SPRING: {
                'focus_areas': ['deep_cleaning', 'decluttering', 'window_cleaning'],
                'priority_boost': ['organization', 'outdoor_prep'],
                'frequency_multiplier': 1.3,
                'special_tasks': ['spring_cleaning', 'allergen_removal', 'fresh_air']
            },
            Season.SUMMER: {
                'focus_areas': ['cooling_systems', 'outdoor_spaces', 'humidity_control'],
                'priority_boost': ['air_conditioning', 'ventilation'],
                'frequency_multiplier': 1.1,
                'special_tasks': ['ac_maintenance', 'outdoor_cleaning', 'pest_control']
            },
            Season.FALL: {
                'focus_areas': ['heating_prep', 'leaf_management', 'weatherproofing'],
                'priority_boost': ['heating_systems', 'insulation'],
                'frequency_multiplier': 1.2,
                'special_tasks': ['heating_check', 'gutter_cleaning', 'winterization']
            },
            Season.WINTER: {
                'focus_areas': ['indoor_air_quality', 'heating_efficiency', 'moisture_control'],
                'priority_boost': ['heating', 'humidity_control'],
                'frequency_multiplier': 0.9,
                'special_tasks': ['humidifier_maintenance', 'draft_sealing', 'indoor_plants']
            }
        }
    
    def detect_room_type(self, zone_name: str, zone_purpose: str, detected_objects: List[str]) -> RoomType:
        """
        Detect room type based on zone information and detected objects
        
        Args:
            zone_name: Name of the zone
            zone_purpose: Purpose description of the zone
            detected_objects: List of objects detected in the scene
            
        Returns:
            Detected room type
        """
        # Normalize inputs
        zone_name_lower = zone_name.lower()
        zone_purpose_lower = zone_purpose.lower()
        detected_objects_lower = [obj.lower() for obj in detected_objects]
        
        # Score each room type
        room_scores = {}
        
        for room_type, patterns in self.room_patterns.items():
            score = 0
            
            # Check zone name and purpose for keywords
            for keyword in patterns['keywords']:
                if keyword in zone_name_lower:
                    score += 3
                if keyword in zone_purpose_lower:
                    score += 2
            
            # Check detected objects
            for obj in patterns['objects']:
                if obj in detected_objects_lower:
                    score += 1
            
            room_scores[room_type] = score
        
        # Return the highest scoring room type
        if room_scores:
            best_room = max(room_scores, key=room_scores.get)
            if room_scores[best_room] > 0:
                return best_room
        
        return RoomType.UNKNOWN
    
    def get_current_season(self) -> Season:
        """Get current season based on date"""
        now = datetime.now()
        month = now.month
        
        if month in [3, 4, 5]:
            return Season.SPRING
        elif month in [6, 7, 8]:
            return Season.SUMMER
        elif month in [9, 10, 11]:
            return Season.FALL
        else:
            return Season.WINTER
    
    def get_time_of_day(self) -> TimeOfDay:
        """Get current time of day context"""
        now = datetime.now()
        hour = now.hour
        
        if 5 <= hour < 12:
            return TimeOfDay.MORNING
        elif 12 <= hour < 17:
            return TimeOfDay.AFTERNOON
        elif 17 <= hour < 22:
            return TimeOfDay.EVENING
        else:
            return TimeOfDay.NIGHT
    
    def extract_objects_from_analysis(self, ai_response: str) -> List[str]:
        """
        Extract object mentions from AI analysis response
        
        Args:
            ai_response: AI analysis response text
            
        Returns:
            List of detected objects
        """
        objects = set()
        
        # Common object patterns to look for
        object_patterns = [
            r'\b(counter|countertop|surface|table|desk|floor|wall|ceiling)\b',
            r'\b(stove|oven|refrigerator|sink|dishwasher|microwave|cabinet)\b',
            r'\b(toilet|shower|bathtub|mirror|towel)\b',
            r'\b(bed|pillow|blanket|dresser|nightstand|closet)\b',
            r'\b(sofa|couch|chair|tv|coffee table|lamp)\b',
            r'\b(computer|monitor|keyboard|mouse|printer|phone)\b',
            r'\b(washer|dryer|laundry|clothes|iron)\b'
        ]
        
        for pattern in object_patterns:
            matches = re.findall(pattern, ai_response.lower())
            objects.update(matches)
        
        return list(objects)
    
    def analyze_scene_context(self, zone_name: str, zone_purpose: str, 
                            ai_response: str) -> SceneContext:
        """
        Analyze scene context from zone information and AI response
        
        Args:
            zone_name: Name of the zone
            zone_purpose: Purpose of the zone
            ai_response: AI analysis response
            
        Returns:
            Scene context analysis
        """
        # Extract objects from AI response
        detected_objects = self.extract_objects_from_analysis(ai_response)
        
        # Detect room type
        room_type = self.detect_room_type(zone_name, zone_purpose, detected_objects)
        
        # Get temporal context
        current_season = self.get_current_season()
        current_time = self.get_time_of_day()
        
        # Analyze lighting and cleanliness indicators
        lighting_condition = self._analyze_lighting(ai_response)
        cleanliness_indicators = self._extract_cleanliness_indicators(ai_response)
        
        return SceneContext(
            room_type=room_type,
            detected_objects=detected_objects,
            lighting_condition=lighting_condition,
            time_of_day=current_time,
            season=current_season,
            cleanliness_indicators=cleanliness_indicators
        )
    
    def _analyze_lighting(self, ai_response: str) -> str:
        """Analyze lighting conditions from AI response"""
        response_lower = ai_response.lower()
        
        if any(word in response_lower for word in ['bright', 'well-lit', 'sunny', 'clear']):
            return "bright"
        elif any(word in response_lower for word in ['dim', 'dark', 'shadowy', 'poor lighting']):
            return "dim"
        elif any(word in response_lower for word in ['natural', 'daylight', 'window']):
            return "natural"
        else:
            return "normal"
    
    def _extract_cleanliness_indicators(self, ai_response: str) -> List[str]:
        """Extract cleanliness indicators from AI response"""
        indicators = []
        response_lower = ai_response.lower()
        
        # Positive indicators
        if any(word in response_lower for word in ['clean', 'tidy', 'organized', 'neat']):
            indicators.append("clean")
        
        # Negative indicators
        if any(word in response_lower for word in ['dirty', 'messy', 'cluttered', 'disorganized']):
            indicators.append("needs_cleaning")
        
        if any(word in response_lower for word in ['dust', 'dusty', 'grimy']):
            indicators.append("dusty")
        
        if any(word in response_lower for word in ['stain', 'spot', 'mark']):
            indicators.append("stained")
        
        if any(word in response_lower for word in ['clutter', 'scattered', 'items everywhere']):
            indicators.append("cluttered")
        
        return indicators

    def generate_contextual_insights(self, scene_context: SceneContext,
                                   ai_analysis: Dict[str, Any]) -> List[ContextualInsight]:
        """
        Generate contextual insights based on scene understanding

        Args:
            scene_context: Scene context analysis
            ai_analysis: Original AI analysis results

        Returns:
            List of contextual insights
        """
        insights = []

        # Room-specific insights
        room_insights = self._generate_room_specific_insights(scene_context)
        insights.extend(room_insights)

        # Seasonal insights
        seasonal_insights = self._generate_seasonal_insights(scene_context)
        insights.extend(seasonal_insights)

        # Time-based insights
        time_insights = self._generate_time_based_insights(scene_context)
        insights.extend(time_insights)

        # Object-specific insights
        object_insights = self._generate_object_insights(scene_context)
        insights.extend(object_insights)

        return insights

    def _generate_room_specific_insights(self, context: SceneContext) -> List[ContextualInsight]:
        """Generate insights specific to the room type"""
        insights = []

        if context.room_type == RoomType.KITCHEN:
            if 'stained' in context.cleanliness_indicators:
                insights.append(ContextualInsight(
                    insight_type="room_specific",
                    description="Kitchen stains require immediate attention for food safety",
                    confidence=0.9,
                    room_specific=True,
                    seasonal_relevance=False,
                    time_sensitive=True,
                    supporting_evidence=["food safety", "hygiene requirements"]
                ))

            if context.time_of_day in [TimeOfDay.MORNING, TimeOfDay.EVENING]:
                insights.append(ContextualInsight(
                    insight_type="usage_pattern",
                    description="Peak kitchen usage time - prioritize quick cleaning tasks",
                    confidence=0.8,
                    room_specific=True,
                    seasonal_relevance=False,
                    time_sensitive=True,
                    supporting_evidence=["meal preparation times"]
                ))

        elif context.room_type == RoomType.BATHROOM:
            if 'needs_cleaning' in context.cleanliness_indicators:
                insights.append(ContextualInsight(
                    insight_type="hygiene_priority",
                    description="Bathroom cleanliness is critical for health and hygiene",
                    confidence=0.95,
                    room_specific=True,
                    seasonal_relevance=False,
                    time_sensitive=True,
                    supporting_evidence=["health requirements", "hygiene standards"]
                ))

        elif context.room_type == RoomType.BEDROOM:
            if context.time_of_day == TimeOfDay.MORNING:
                insights.append(ContextualInsight(
                    insight_type="daily_routine",
                    description="Morning is ideal time for bedroom organization",
                    confidence=0.7,
                    room_specific=True,
                    seasonal_relevance=False,
                    time_sensitive=True,
                    supporting_evidence=["daily routine optimization"]
                ))

        return insights

    def _generate_seasonal_insights(self, context: SceneContext) -> List[ContextualInsight]:
        """Generate seasonal cleaning insights"""
        insights = []

        seasonal_data = self.seasonal_adjustments[context.season]

        # Check if any detected objects need seasonal attention
        for focus_area in seasonal_data['focus_areas']:
            if any(focus_area in obj for obj in context.detected_objects):
                insights.append(ContextualInsight(
                    insight_type="seasonal_priority",
                    description=f"{context.season.value.title()} focus: {focus_area} requires attention",
                    confidence=0.8,
                    room_specific=False,
                    seasonal_relevance=True,
                    time_sensitive=False,
                    supporting_evidence=[f"{context.season.value} maintenance"]
                ))

        # Seasonal frequency adjustments
        if seasonal_data['frequency_multiplier'] > 1.0:
            insights.append(ContextualInsight(
                insight_type="frequency_adjustment",
                description=f"Increase cleaning frequency by {(seasonal_data['frequency_multiplier'] - 1) * 100:.0f}% for {context.season.value}",
                confidence=0.7,
                room_specific=False,
                seasonal_relevance=True,
                time_sensitive=False,
                supporting_evidence=[f"{context.season.value} requirements"]
            ))

        return insights

    def _generate_time_based_insights(self, context: SceneContext) -> List[ContextualInsight]:
        """Generate time-of-day based insights"""
        insights = []

        if context.time_of_day == TimeOfDay.MORNING:
            insights.append(ContextualInsight(
                insight_type="optimal_timing",
                description="Morning is optimal for quick tidying and organization tasks",
                confidence=0.7,
                room_specific=False,
                seasonal_relevance=False,
                time_sensitive=True,
                supporting_evidence=["energy levels", "daily routine"]
            ))

        elif context.time_of_day == TimeOfDay.EVENING:
            insights.append(ContextualInsight(
                insight_type="optimal_timing",
                description="Evening is good for deeper cleaning tasks and preparation for next day",
                confidence=0.6,
                room_specific=False,
                seasonal_relevance=False,
                time_sensitive=True,
                supporting_evidence=["available time", "next day preparation"]
            ))

        return insights

    def _generate_object_insights(self, context: SceneContext) -> List[ContextualInsight]:
        """Generate insights based on detected objects"""
        insights = []

        # Check object database for specific cleaning requirements
        for category, data in self.object_database.items():
            detected_category_objects = [obj for obj in context.detected_objects
                                       if obj in data['objects']]

            if detected_category_objects:
                if data['priority_level'] == 'high':
                    insights.append(ContextualInsight(
                        insight_type="object_priority",
                        description=f"High-priority objects detected: {', '.join(detected_category_objects)}",
                        confidence=0.8,
                        room_specific=True,
                        seasonal_relevance=False,
                        time_sensitive=True,
                        supporting_evidence=[f"{category} maintenance requirements"]
                    ))

        return insights

    def enhance_ai_prompt(self, base_prompt: str, scene_context: SceneContext) -> str:
        """
        Enhance AI prompt with contextual information

        Args:
            base_prompt: Original AI prompt
            scene_context: Scene context analysis

        Returns:
            Enhanced prompt with context
        """
        # Add room type context
        room_context = f"\nRoom Type: {scene_context.room_type.value.replace('_', ' ').title()}"

        # Add seasonal context
        seasonal_data = self.seasonal_adjustments[scene_context.season]
        seasonal_context = f"\nSeasonal Context ({scene_context.season.value.title()}): Focus on {', '.join(seasonal_data['focus_areas'])}"

        # Add time context
        time_context = f"\nTime Context: {scene_context.time_of_day.value.title()} - consider appropriate task timing"

        # Add detected objects context
        if scene_context.detected_objects:
            objects_context = f"\nDetected Objects: {', '.join(scene_context.detected_objects[:10])}"  # Limit to first 10
        else:
            objects_context = ""

        # Add lighting context
        lighting_context = f"\nLighting: {scene_context.lighting_condition}"

        # Combine all context
        enhanced_prompt = (
            base_prompt +
            room_context +
            seasonal_context +
            time_context +
            objects_context +
            lighting_context +
            "\n\nPlease consider this contextual information when analyzing the image and generating recommendations."
        )

        return enhanced_prompt

    def prioritize_tasks_with_context(self, tasks: List[Dict], scene_context: SceneContext) -> List[Dict]:
        """
        Prioritize tasks based on contextual understanding

        Args:
            tasks: List of tasks to prioritize
            scene_context: Scene context analysis

        Returns:
            Prioritized list of tasks
        """
        # Create a copy to avoid modifying original
        prioritized_tasks = [task.copy() for task in tasks]

        seasonal_data = self.seasonal_adjustments[scene_context.season]

        for task in prioritized_tasks:
            original_priority = task.get('priority', 5)
            context_boost = 0

            # Room-specific priority boosts
            if scene_context.room_type == RoomType.KITCHEN:
                if any(keyword in task.get('description', '').lower()
                      for keyword in ['clean', 'sanitize', 'food', 'counter']):
                    context_boost += 2

            elif scene_context.room_type == RoomType.BATHROOM:
                if any(keyword in task.get('description', '').lower()
                      for keyword in ['clean', 'sanitize', 'disinfect']):
                    context_boost += 3

            # Seasonal priority boosts
            for boost_area in seasonal_data['priority_boost']:
                if boost_area in task.get('description', '').lower():
                    context_boost += 1

            # Time-based adjustments
            if scene_context.time_of_day == TimeOfDay.MORNING:
                if any(keyword in task.get('description', '').lower()
                      for keyword in ['organize', 'tidy', 'quick']):
                    context_boost += 1

            # Apply frequency multiplier
            frequency_boost = (seasonal_data['frequency_multiplier'] - 1.0) * 2
            context_boost += frequency_boost

            # Update priority (cap at 10)
            task['priority'] = min(10, original_priority + context_boost)
            task['context_boost'] = context_boost
            task['original_priority'] = original_priority

        # Sort by priority (highest first)
        prioritized_tasks.sort(key=lambda x: x.get('priority', 5), reverse=True)

        return prioritized_tasks

    def get_context_summary(self, scene_context: SceneContext) -> Dict[str, Any]:
        """
        Get a summary of the scene context

        Args:
            scene_context: Scene context analysis

        Returns:
            Context summary dictionary
        """
        return {
            'room_type': scene_context.room_type.value,
            'season': scene_context.season.value,
            'time_of_day': scene_context.time_of_day.value,
            'lighting': scene_context.lighting_condition,
            'detected_objects_count': len(scene_context.detected_objects),
            'detected_objects': scene_context.detected_objects[:10],  # First 10 objects
            'cleanliness_indicators': scene_context.cleanliness_indicators,
            'seasonal_focus_areas': self.seasonal_adjustments[scene_context.season]['focus_areas'],
            'seasonal_frequency_multiplier': self.seasonal_adjustments[scene_context.season]['frequency_multiplier'],
            'context_timestamp': datetime.now(timezone.utc).isoformat()
        }
