"""
Gamification System for AICleaner
Implements achievement system, progress tracking, and motivational features
"""
import os
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
import statistics


class AchievementType(Enum):
    """Types of achievements"""
    TASK_COMPLETION = "task_completion"
    STREAK = "streak"
    EFFICIENCY = "efficiency"
    CONSISTENCY = "consistency"
    MILESTONE = "milestone"
    SPECIAL = "special"


class AchievementRarity(Enum):
    """Achievement rarity levels"""
    COMMON = "common"
    UNCOMMON = "uncommon"
    RARE = "rare"
    EPIC = "epic"
    LEGENDARY = "legendary"


@dataclass
class Achievement:
    """Represents an achievement"""
    id: str
    title: str
    description: str
    icon: str
    type: AchievementType
    rarity: AchievementRarity
    points: int
    requirements: Dict[str, Any]
    unlocked: bool = False
    unlocked_date: Optional[str] = None
    progress: float = 0.0
    hidden: bool = False


@dataclass
class UserStats:
    """User statistics for gamification"""
    total_tasks_completed: int = 0
    total_zones_cleaned: int = 0
    current_streak: int = 0
    longest_streak: int = 0
    total_points: int = 0
    level: int = 1
    experience: int = 0
    achievements_unlocked: int = 0
    efficiency_score: float = 0.0
    consistency_score: float = 0.0
    last_activity_date: Optional[str] = None
    join_date: str = ""


@dataclass
class DailyChallenge:
    """Daily challenge for users"""
    id: str
    title: str
    description: str
    icon: str
    target: int
    current_progress: int
    points_reward: int
    date: str
    completed: bool = False
    challenge_type: str = "task_completion"


class GamificationSystem:
    """
    Gamification system for AICleaner
    
    Features:
    - Achievement system with multiple types and rarities
    - Experience points and leveling system
    - Daily challenges and streaks
    - Progress tracking and statistics
    - Motivational features and rewards
    - Leaderboards and social features
    """
    
    def __init__(self, data_path: str = "/data/gamification"):
        """
        Initialize gamification system
        
        Args:
            data_path: Path to store gamification data
        """
        self.data_path = data_path
        self.logger = logging.getLogger(__name__)
        
        # Ensure data directory exists
        os.makedirs(data_path, exist_ok=True)
        
        # Load user data and achievements
        self.user_stats = self._load_user_stats()
        self.achievements = self._load_achievements()
        self.daily_challenges = self._load_daily_challenges()
        
        # Initialize achievement definitions
        self._initialize_achievements()
        
        self.logger.info("Gamification system initialized")
    
    def _load_user_stats(self) -> UserStats:
        """Load user statistics from file"""
        stats_file = os.path.join(self.data_path, "user_stats.json")
        
        if os.path.exists(stats_file):
            try:
                with open(stats_file, 'r') as f:
                    data = json.load(f)
                return UserStats(**data)
            except Exception as e:
                self.logger.error(f"Error loading user stats: {e}")
        
        # Return default stats with current date
        return UserStats(join_date=datetime.now(timezone.utc).isoformat())
    
    def _save_user_stats(self):
        """Save user statistics to file"""
        stats_file = os.path.join(self.data_path, "user_stats.json")
        
        try:
            with open(stats_file, 'w') as f:
                json.dump(asdict(self.user_stats), f, indent=2)
            self.logger.debug("User stats saved")
        except Exception as e:
            self.logger.error(f"Error saving user stats: {e}")
    
    def _load_achievements(self) -> List[Achievement]:
        """Load achievements from file"""
        achievements_file = os.path.join(self.data_path, "achievements.json")
        
        if os.path.exists(achievements_file):
            try:
                with open(achievements_file, 'r') as f:
                    data = json.load(f)
                achievements = []
                for item in data:
                    # Convert enum strings back to enums
                    item['type'] = AchievementType(item['type'])
                    item['rarity'] = AchievementRarity(item['rarity'])
                    achievements.append(Achievement(**item))
                return achievements
            except Exception as e:
                self.logger.error(f"Error loading achievements: {e}")
        
        return []
    
    def _save_achievements(self):
        """Save achievements to file"""
        achievements_file = os.path.join(self.data_path, "achievements.json")
        
        try:
            # Convert achievements to serializable format
            data = []
            for achievement in self.achievements:
                achievement_dict = asdict(achievement)
                achievement_dict['type'] = achievement.type.value
                achievement_dict['rarity'] = achievement.rarity.value
                data.append(achievement_dict)
            
            with open(achievements_file, 'w') as f:
                json.dump(data, f, indent=2)
            self.logger.debug("Achievements saved")
        except Exception as e:
            self.logger.error(f"Error saving achievements: {e}")
    
    def _load_daily_challenges(self) -> List[DailyChallenge]:
        """Load daily challenges from file"""
        challenges_file = os.path.join(self.data_path, "daily_challenges.json")
        
        if os.path.exists(challenges_file):
            try:
                with open(challenges_file, 'r') as f:
                    data = json.load(f)
                return [DailyChallenge(**item) for item in data]
            except Exception as e:
                self.logger.error(f"Error loading daily challenges: {e}")
        
        return []
    
    def _save_daily_challenges(self):
        """Save daily challenges to file"""
        challenges_file = os.path.join(self.data_path, "daily_challenges.json")
        
        try:
            with open(challenges_file, 'w') as f:
                json.dump([asdict(challenge) for challenge in self.daily_challenges], f, indent=2)
            self.logger.debug("Daily challenges saved")
        except Exception as e:
            self.logger.error(f"Error saving daily challenges: {e}")
    
    def _initialize_achievements(self):
        """Initialize default achievements if none exist"""
        if self.achievements:
            return  # Achievements already exist
        
        default_achievements = [
            # Task Completion Achievements
            Achievement(
                id="first_task",
                title="Getting Started",
                description="Complete your first cleaning task",
                icon="mdi:star-outline",
                type=AchievementType.TASK_COMPLETION,
                rarity=AchievementRarity.COMMON,
                points=10,
                requirements={"tasks_completed": 1}
            ),
            Achievement(
                id="task_master_10",
                title="Task Master",
                description="Complete 10 cleaning tasks",
                icon="mdi:star",
                type=AchievementType.TASK_COMPLETION,
                rarity=AchievementRarity.UNCOMMON,
                points=50,
                requirements={"tasks_completed": 10}
            ),
            Achievement(
                id="task_legend_100",
                title="Cleaning Legend",
                description="Complete 100 cleaning tasks",
                icon="mdi:star-four-points",
                type=AchievementType.TASK_COMPLETION,
                rarity=AchievementRarity.RARE,
                points=200,
                requirements={"tasks_completed": 100}
            ),
            
            # Streak Achievements
            Achievement(
                id="streak_3",
                title="On a Roll",
                description="Maintain a 3-day cleaning streak",
                icon="mdi:fire",
                type=AchievementType.STREAK,
                rarity=AchievementRarity.COMMON,
                points=25,
                requirements={"streak_days": 3}
            ),
            Achievement(
                id="streak_7",
                title="Week Warrior",
                description="Maintain a 7-day cleaning streak",
                icon="mdi:fire",
                type=AchievementType.STREAK,
                rarity=AchievementRarity.UNCOMMON,
                points=75,
                requirements={"streak_days": 7}
            ),
            Achievement(
                id="streak_30",
                title="Consistency Champion",
                description="Maintain a 30-day cleaning streak",
                icon="mdi:fire",
                type=AchievementType.STREAK,
                rarity=AchievementRarity.EPIC,
                points=300,
                requirements={"streak_days": 30}
            ),
            
            # Efficiency Achievements
            Achievement(
                id="efficiency_master",
                title="Efficiency Master",
                description="Achieve 90% efficiency score",
                icon="mdi:speedometer",
                type=AchievementType.EFFICIENCY,
                rarity=AchievementRarity.RARE,
                points=150,
                requirements={"efficiency_score": 0.9}
            ),
            
            # Milestone Achievements
            Achievement(
                id="level_10",
                title="Rising Star",
                description="Reach level 10",
                icon="mdi:trophy-outline",
                type=AchievementType.MILESTONE,
                rarity=AchievementRarity.UNCOMMON,
                points=100,
                requirements={"level": 10}
            ),
            Achievement(
                id="level_25",
                title="Cleaning Expert",
                description="Reach level 25",
                icon="mdi:trophy",
                type=AchievementType.MILESTONE,
                rarity=AchievementRarity.RARE,
                points=250,
                requirements={"level": 25}
            ),
            
            # Special Achievements
            Achievement(
                id="early_bird",
                title="Early Bird",
                description="Complete tasks before 8 AM",
                icon="mdi:weather-sunrise",
                type=AchievementType.SPECIAL,
                rarity=AchievementRarity.UNCOMMON,
                points=50,
                requirements={"early_morning_tasks": 5}
            ),
            Achievement(
                id="night_owl",
                title="Night Owl",
                description="Complete tasks after 10 PM",
                icon="mdi:weather-night",
                type=AchievementType.SPECIAL,
                rarity=AchievementRarity.UNCOMMON,
                points=50,
                requirements={"late_night_tasks": 5}
            ),
            Achievement(
                id="perfectionist",
                title="Perfectionist",
                description="Complete 10 tasks with perfect scores",
                icon="mdi:diamond-stone",
                type=AchievementType.SPECIAL,
                rarity=AchievementRarity.EPIC,
                points=200,
                requirements={"perfect_tasks": 10},
                hidden=True
            )
        ]
        
        self.achievements = default_achievements
        self._save_achievements()
        self.logger.info(f"Initialized {len(default_achievements)} default achievements")

    def record_task_completion(self, zone_name: str, task_description: str,
                             completion_time: datetime, task_score: float = 1.0):
        """
        Record a task completion and update gamification stats

        Args:
            zone_name: Name of the zone
            task_description: Description of the completed task
            completion_time: When the task was completed
            task_score: Quality score of the task (0.0-1.0)
        """
        # Update basic stats
        self.user_stats.total_tasks_completed += 1
        self.user_stats.last_activity_date = completion_time.isoformat()

        # Update streak
        self._update_streak(completion_time)

        # Award experience points
        base_points = 10
        quality_bonus = int(task_score * 10)
        streak_bonus = min(self.user_stats.current_streak * 2, 20)
        total_points = base_points + quality_bonus + streak_bonus

        self._award_experience(total_points)

        # Check for achievements
        self._check_achievements(completion_time, task_score)

        # Update daily challenges
        self._update_daily_challenges()

        # Save data
        self._save_user_stats()

        self.logger.debug(f"Task completion recorded: {zone_name} - {task_description}")

    def _update_streak(self, completion_time: datetime):
        """Update the user's streak based on completion time"""
        if not self.user_stats.last_activity_date:
            self.user_stats.current_streak = 1
            return

        last_activity = datetime.fromisoformat(self.user_stats.last_activity_date.replace('Z', '+00:00'))
        days_since_last = (completion_time.date() - last_activity.date()).days

        if days_since_last == 0:
            # Same day, streak continues
            pass
        elif days_since_last == 1:
            # Next day, increment streak
            self.user_stats.current_streak += 1
            if self.user_stats.current_streak > self.user_stats.longest_streak:
                self.user_stats.longest_streak = self.user_stats.current_streak
        else:
            # Streak broken
            self.user_stats.current_streak = 1

    def _award_experience(self, points: int):
        """Award experience points and handle leveling up"""
        self.user_stats.experience += points
        self.user_stats.total_points += points

        # Calculate level (100 XP per level, with increasing requirements)
        required_xp = 0
        level = 1

        while required_xp <= self.user_stats.experience:
            level += 1
            required_xp += level * 100  # Increasing XP requirement per level

        old_level = self.user_stats.level
        self.user_stats.level = level - 1

        # Check for level-up achievements
        if self.user_stats.level > old_level:
            self.logger.info(f"User leveled up to level {self.user_stats.level}")
            self._check_level_achievements()

    def _check_achievements(self, completion_time: datetime, task_score: float):
        """Check and unlock achievements based on current stats"""
        newly_unlocked = []

        for achievement in self.achievements:
            if achievement.unlocked:
                continue

            # Update progress and check if unlocked
            progress = self._calculate_achievement_progress(achievement)
            achievement.progress = progress

            if progress >= 1.0:
                achievement.unlocked = True
                achievement.unlocked_date = completion_time.isoformat()
                self.user_stats.achievements_unlocked += 1
                self.user_stats.total_points += achievement.points
                newly_unlocked.append(achievement)

                self.logger.info(f"Achievement unlocked: {achievement.title}")

        if newly_unlocked:
            self._save_achievements()

        return newly_unlocked

    def _calculate_achievement_progress(self, achievement: Achievement) -> float:
        """Calculate progress towards an achievement"""
        requirements = achievement.requirements

        if achievement.type == AchievementType.TASK_COMPLETION:
            required = requirements.get('tasks_completed', 1)
            return min(1.0, self.user_stats.total_tasks_completed / required)

        elif achievement.type == AchievementType.STREAK:
            required = requirements.get('streak_days', 1)
            return min(1.0, self.user_stats.current_streak / required)

        elif achievement.type == AchievementType.EFFICIENCY:
            required = requirements.get('efficiency_score', 1.0)
            return min(1.0, self.user_stats.efficiency_score / required)

        elif achievement.type == AchievementType.MILESTONE:
            required = requirements.get('level', 1)
            return min(1.0, self.user_stats.level / required)

        elif achievement.type == AchievementType.SPECIAL:
            # Special achievements have custom logic
            if 'perfect_tasks' in requirements:
                # This would need to be tracked separately
                return 0.0  # Placeholder
            elif 'early_morning_tasks' in requirements:
                # This would need to be tracked separately
                return 0.0  # Placeholder
            elif 'late_night_tasks' in requirements:
                # This would need to be tracked separately
                return 0.0  # Placeholder

        return 0.0

    def _check_level_achievements(self):
        """Check for level-based achievements"""
        for achievement in self.achievements:
            if (achievement.type == AchievementType.MILESTONE and
                not achievement.unlocked and
                'level' in achievement.requirements):

                required_level = achievement.requirements['level']
                if self.user_stats.level >= required_level:
                    achievement.unlocked = True
                    achievement.unlocked_date = datetime.now(timezone.utc).isoformat()
                    achievement.progress = 1.0
                    self.user_stats.achievements_unlocked += 1
                    self.user_stats.total_points += achievement.points

                    self.logger.info(f"Level achievement unlocked: {achievement.title}")

    def _update_daily_challenges(self):
        """Update progress on daily challenges"""
        today = datetime.now(timezone.utc).date().isoformat()

        # Create today's challenge if it doesn't exist
        if not any(challenge.date == today for challenge in self.daily_challenges):
            self._generate_daily_challenge(today)

        # Update progress on today's challenges
        for challenge in self.daily_challenges:
            if challenge.date == today and not challenge.completed:
                if challenge.challenge_type == "task_completion":
                    challenge.current_progress = min(challenge.target,
                                                   self.user_stats.total_tasks_completed)
                    if challenge.current_progress >= challenge.target:
                        challenge.completed = True
                        self.user_stats.total_points += challenge.points_reward
                        self.logger.info(f"Daily challenge completed: {challenge.title}")

        # Clean up old challenges (keep last 7 days)
        cutoff_date = (datetime.now(timezone.utc) - timedelta(days=7)).date().isoformat()
        self.daily_challenges = [c for c in self.daily_challenges if c.date >= cutoff_date]

        self._save_daily_challenges()

    def _generate_daily_challenge(self, date: str):
        """Generate a daily challenge for the given date"""
        import random

        challenges = [
            {
                "title": "Daily Cleaner",
                "description": "Complete 3 cleaning tasks today",
                "icon": "mdi:broom",
                "target": 3,
                "points_reward": 30,
                "challenge_type": "task_completion"
            },
            {
                "title": "Zone Master",
                "description": "Clean 2 different zones today",
                "icon": "mdi:home-variant",
                "target": 2,
                "points_reward": 25,
                "challenge_type": "zone_completion"
            },
            {
                "title": "Speed Cleaner",
                "description": "Complete 5 tasks today",
                "icon": "mdi:timer",
                "target": 5,
                "points_reward": 50,
                "challenge_type": "task_completion"
            }
        ]

        challenge_template = random.choice(challenges)
        challenge = DailyChallenge(
            id=f"daily_{date}",
            date=date,
            current_progress=0,
            **challenge_template
        )

        self.daily_challenges.append(challenge)
        self.logger.info(f"Generated daily challenge: {challenge.title}")

    def get_user_stats(self) -> Dict[str, Any]:
        """Get current user statistics"""
        return asdict(self.user_stats)

    def get_achievements(self, include_locked: bool = True) -> List[Dict[str, Any]]:
        """
        Get achievements list

        Args:
            include_locked: Whether to include locked achievements

        Returns:
            List of achievements
        """
        achievements = []
        for achievement in self.achievements:
            if not include_locked and not achievement.unlocked and achievement.hidden:
                continue

            achievement_dict = asdict(achievement)
            achievement_dict['type'] = achievement.type.value
            achievement_dict['rarity'] = achievement.rarity.value
            achievements.append(achievement_dict)

        return achievements

    def get_daily_challenges(self) -> List[Dict[str, Any]]:
        """Get current daily challenges"""
        today = datetime.now(timezone.utc).date().isoformat()
        return [asdict(challenge) for challenge in self.daily_challenges
                if challenge.date == today]

    def get_leaderboard_data(self) -> Dict[str, Any]:
        """Get leaderboard data for the user"""
        # In a multi-user system, this would compare with other users
        # For now, return personal best data
        return {
            'current_level': self.user_stats.level,
            'total_points': self.user_stats.total_points,
            'achievements_count': self.user_stats.achievements_unlocked,
            'longest_streak': self.user_stats.longest_streak,
            'total_tasks': self.user_stats.total_tasks_completed,
            'efficiency_score': self.user_stats.efficiency_score,
            'rank': 1,  # Placeholder for single user
            'percentile': 100  # Placeholder for single user
        }

    def get_progress_summary(self) -> Dict[str, Any]:
        """Get progress summary for dashboard"""
        # Calculate next level progress
        current_level_xp = sum(i * 100 for i in range(1, self.user_stats.level + 1))
        next_level_xp = current_level_xp + (self.user_stats.level + 1) * 100
        level_progress = (self.user_stats.experience - current_level_xp) / ((self.user_stats.level + 1) * 100)

        # Get recent achievements
        recent_achievements = [
            achievement for achievement in self.achievements
            if achievement.unlocked and achievement.unlocked_date
        ]
        recent_achievements.sort(key=lambda x: x.unlocked_date, reverse=True)
        recent_achievements = recent_achievements[:3]  # Last 3 achievements

        # Get active daily challenges
        daily_challenges = self.get_daily_challenges()

        return {
            'level': self.user_stats.level,
            'level_progress': min(1.0, max(0.0, level_progress)),
            'experience': self.user_stats.experience,
            'next_level_xp': next_level_xp,
            'total_points': self.user_stats.total_points,
            'current_streak': self.user_stats.current_streak,
            'achievements_unlocked': self.user_stats.achievements_unlocked,
            'total_achievements': len(self.achievements),
            'recent_achievements': [asdict(a) for a in recent_achievements],
            'daily_challenges': daily_challenges,
            'efficiency_score': self.user_stats.efficiency_score
        }

    def get_motivational_message(self) -> Dict[str, str]:
        """Get a motivational message based on current progress"""
        import random

        messages = {
            'streak': [
                f"🔥 Amazing! You're on a {self.user_stats.current_streak}-day streak!",
                f"🌟 Keep it up! {self.user_stats.current_streak} days of consistent cleaning!",
                f"💪 Unstoppable! {self.user_stats.current_streak} days and counting!"
            ],
            'level_up': [
                f"🎉 Congratulations! You've reached level {self.user_stats.level}!",
                f"⭐ Level {self.user_stats.level} achieved! You're becoming a cleaning master!",
                f"🏆 Welcome to level {self.user_stats.level}! Your dedication is paying off!"
            ],
            'achievement': [
                "🏅 New achievement unlocked! Check your achievements page!",
                "🎯 You've earned a new badge! Great work!",
                "✨ Achievement earned! You're making excellent progress!"
            ],
            'encouragement': [
                "🌈 Every small step counts towards a cleaner home!",
                "💫 You're doing great! Keep up the momentum!",
                "🌟 Your consistency is inspiring! One task at a time!",
                "🎯 Focus on progress, not perfection!",
                "💪 You've got this! Every clean space makes a difference!"
            ],
            'milestone': [
                f"🎊 Incredible! You've completed {self.user_stats.total_tasks_completed} tasks!",
                f"🏆 {self.user_stats.total_points} points earned! You're a cleaning champion!",
                f"⭐ {self.user_stats.achievements_unlocked} achievements unlocked! Amazing progress!"
            ]
        }

        # Choose message type based on current state
        if self.user_stats.current_streak >= 7:
            message_type = 'streak'
        elif self.user_stats.level >= 10:
            message_type = 'milestone'
        elif self.user_stats.achievements_unlocked > 0:
            message_type = 'achievement'
        else:
            message_type = 'encouragement'

        message = random.choice(messages[message_type])

        return {
            'message': message,
            'type': message_type,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

    def reset_daily_progress(self):
        """Reset daily progress (called at midnight)"""
        # This would be called by a scheduler
        today = datetime.now(timezone.utc).date().isoformat()

        # Generate new daily challenge
        if not any(challenge.date == today for challenge in self.daily_challenges):
            self._generate_daily_challenge(today)

        self.logger.info("Daily progress reset and new challenges generated")

    def export_gamification_data(self) -> Dict[str, Any]:
        """Export complete gamification data"""
        return {
            'user_stats': asdict(self.user_stats),
            'achievements': self.get_achievements(include_locked=True),
            'daily_challenges': [asdict(challenge) for challenge in self.daily_challenges],
            'leaderboard': self.get_leaderboard_data(),
            'progress_summary': self.get_progress_summary(),
            'motivational_message': self.get_motivational_message(),
            'system_info': {
                'total_achievements_available': len(self.achievements),
                'achievement_types': list(set(a.type.value for a in self.achievements)),
                'rarity_levels': list(set(a.rarity.value for a in self.achievements)),
                'data_path': self.data_path,
                'last_updated': datetime.now(timezone.utc).isoformat()
            }
        }

    def get_system_status(self) -> Dict[str, Any]:
        """Get gamification system status"""
        return {
            'user_stats_loaded': bool(self.user_stats),
            'achievements_loaded': len(self.achievements),
            'daily_challenges_active': len(self.get_daily_challenges()),
            'current_level': self.user_stats.level,
            'total_points': self.user_stats.total_points,
            'achievements_unlocked': self.user_stats.achievements_unlocked,
            'current_streak': self.user_stats.current_streak,
            'data_path': self.data_path,
            'system_health': 'healthy'
        }
