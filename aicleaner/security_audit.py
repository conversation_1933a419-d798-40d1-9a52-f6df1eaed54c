"""
Security Audit and Vulnerability Assessment for AICleaner
Comprehensive security analysis and hardening recommendations
"""

import os
import json
import stat
import hashlib
import re
import subprocess
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class SecurityFinding:
    """Individual security finding"""
    severity: str  # "critical", "high", "medium", "low", "info"
    category: str  # "authentication", "authorization", "input_validation", "crypto", "config", "file_system"
    title: str
    description: str
    location: str
    recommendation: str
    cve_references: List[str] = None
    
    def __post_init__(self):
        if self.cve_references is None:
            self.cve_references = []


@dataclass
class SecurityReport:
    """Complete security audit report"""
    timestamp: str
    overall_score: int  # 0-100
    risk_level: str  # "low", "medium", "high", "critical"
    findings: List[SecurityFinding]
    summary: Dict[str, int]
    recommendations: List[str]
    compliance_status: Dict[str, bool]


class SecurityAuditor:
    """
    Comprehensive security auditor for AICleaner
    
    Features:
    - Input validation analysis
    - Authentication and authorization review
    - File system security assessment
    - Configuration security analysis
    - Dependency vulnerability scanning
    - API security evaluation
    - Cryptographic implementation review
    - Production hardening recommendations
    """
    
    def __init__(self, project_root: str = "/root/addons/Aiclean"):
        """
        Initialize security auditor
        
        Args:
            project_root: Root directory of the AICleaner project
        """
        self.project_root = project_root
        self.data_dir = os.path.join(project_root, "data")
        self.aicleaner_dir = os.path.join(project_root, "aicleaner")
        
        # Security findings
        self.findings = []
        
        # Security patterns to check
        self.security_patterns = {
            "hardcoded_secrets": [
                r"password\s*=\s*['\"][^'\"]+['\"]",
                r"api_key\s*=\s*['\"][^'\"]+['\"]",
                r"secret\s*=\s*['\"][^'\"]+['\"]",
                r"token\s*=\s*['\"][^'\"]+['\"]"
            ],
            "sql_injection": [
                r"execute\s*\(\s*['\"].*%.*['\"]",
                r"query\s*\(\s*['\"].*\+.*['\"]"
            ],
            "command_injection": [
                r"os\.system\s*\(",
                r"subprocess\.call\s*\(",
                r"eval\s*\(",
                r"exec\s*\("
            ],
            "path_traversal": [
                r"\.\.\/",
                r"\.\.\\",
                r"os\.path\.join\s*\([^)]*\.\.[^)]*\)"
            ]
        }
    
    def run_full_audit(self) -> SecurityReport:
        """
        Run comprehensive security audit
        
        Returns:
            Complete security audit report
        """
        print("🔒 Starting comprehensive security audit...")
        
        # Clear previous findings
        self.findings = []
        
        # Run all security checks
        self._check_input_validation()
        self._check_authentication_security()
        self._check_file_system_security()
        self._check_configuration_security()
        self._check_dependency_vulnerabilities()
        self._check_api_security()
        self._check_cryptographic_implementation()
        self._check_production_hardening()
        
        # Generate report
        report = self._generate_security_report()
        
        # Save report
        self._save_security_report(report)
        
        print(f"🔒 Security audit completed - Overall score: {report.overall_score}/100")
        return report
    
    def _check_input_validation(self):
        """Check input validation and sanitization"""
        print("  🔍 Checking input validation...")
        
        # Check Python files for input validation patterns
        python_files = list(Path(self.aicleaner_dir).glob("**/*.py"))
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for user input handling
                if re.search(r"request\.|input\(|raw_input\(", content):
                    # Look for validation patterns
                    has_validation = bool(re.search(r"validate|sanitize|escape|clean", content, re.IGNORECASE))
                    
                    if not has_validation:
                        self.findings.append(SecurityFinding(
                            severity="medium",
                            category="input_validation",
                            title="Missing Input Validation",
                            description=f"File {file_path.name} handles user input but lacks explicit validation",
                            location=str(file_path),
                            recommendation="Implement input validation and sanitization for all user inputs"
                        ))
                
                # Check for dangerous patterns
                for pattern_type, patterns in self.security_patterns.items():
                    for pattern in patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            
                            severity = "high" if pattern_type in ["sql_injection", "command_injection"] else "medium"
                            
                            self.findings.append(SecurityFinding(
                                severity=severity,
                                category="input_validation",
                                title=f"Potential {pattern_type.replace('_', ' ').title()}",
                                description=f"Suspicious pattern found: {match.group()}",
                                location=f"{file_path.name}:{line_num}",
                                recommendation=f"Review and secure {pattern_type.replace('_', ' ')} implementation"
                            ))
                            
            except Exception as e:
                self.findings.append(SecurityFinding(
                    severity="low",
                    category="file_system",
                    title="File Read Error",
                    description=f"Could not read file {file_path}: {str(e)}",
                    location=str(file_path),
                    recommendation="Ensure file permissions are correct"
                ))
    
    def _check_authentication_security(self):
        """Check authentication and authorization mechanisms"""
        print("  🔍 Checking authentication security...")
        
        # Check for API key handling
        config_file = os.path.join(self.data_dir, "options.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                # Check API key security
                api_keys = ['gemini_api_key', 'claude_api_key', 'openai_api_key']
                for key in api_keys:
                    if key in config:
                        api_key_value = config[key]
                        
                        # Check for example/placeholder keys
                        if api_key_value in ["", "your_api_key_here", "AIzaSyExample_API_Key_Replace_With_Your_Key"]:
                            self.findings.append(SecurityFinding(
                                severity="medium",
                                category="authentication",
                                title="Placeholder API Key",
                                description=f"{key} contains placeholder value",
                                location="options.json",
                                recommendation="Replace with valid API key"
                            ))
                        
                        # Check key format (basic validation)
                        elif len(api_key_value) < 20:
                            self.findings.append(SecurityFinding(
                                severity="medium",
                                category="authentication",
                                title="Suspicious API Key Format",
                                description=f"{key} appears to be too short",
                                location="options.json",
                                recommendation="Verify API key is valid and properly formatted"
                            ))
                
            except Exception as e:
                self.findings.append(SecurityFinding(
                    severity="medium",
                    category="authentication",
                    title="Configuration Access Error",
                    description=f"Could not read configuration: {str(e)}",
                    location="options.json",
                    recommendation="Ensure configuration file is accessible and valid"
                ))
        
        # Check for Home Assistant token security
        ha_client_files = list(Path(self.aicleaner_dir).glob("**/ha_client.py"))
        for file_path in ha_client_files:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Check for hardcoded tokens
                if re.search(r"token\s*=\s*['\"][^'\"]{50,}['\"]", content):
                    self.findings.append(SecurityFinding(
                        severity="critical",
                        category="authentication",
                        title="Hardcoded Authentication Token",
                        description="Home Assistant token appears to be hardcoded",
                        location=str(file_path),
                        recommendation="Use environment variables or secure configuration for tokens"
                    ))
                    
            except Exception:
                pass
    
    def _check_file_system_security(self):
        """Check file system permissions and security"""
        print("  🔍 Checking file system security...")
        
        # Check data directory permissions
        if os.path.exists(self.data_dir):
            stat_info = os.stat(self.data_dir)
            permissions = stat.filemode(stat_info.st_mode)
            
            # Check if directory is world-writable
            if stat_info.st_mode & stat.S_IWOTH:
                self.findings.append(SecurityFinding(
                    severity="high",
                    category="file_system",
                    title="World-Writable Data Directory",
                    description=f"Data directory {self.data_dir} is world-writable ({permissions})",
                    location=self.data_dir,
                    recommendation="Restrict data directory permissions to owner only"
                ))
        
        # Check for sensitive files with weak permissions
        sensitive_patterns = ["*.key", "*.pem", "*.p12", "*.pfx", "secrets.*", "*.env"]
        
        for pattern in sensitive_patterns:
            for file_path in Path(self.project_root).glob(f"**/{pattern}"):
                if file_path.is_file():
                    stat_info = file_path.stat()
                    
                    # Check if file is world-readable
                    if stat_info.st_mode & stat.S_IROTH:
                        self.findings.append(SecurityFinding(
                            severity="medium",
                            category="file_system",
                            title="Sensitive File World-Readable",
                            description=f"Sensitive file {file_path.name} is world-readable",
                            location=str(file_path),
                            recommendation="Restrict file permissions to owner only (600)"
                        ))
        
        # Check for temporary files
        temp_patterns = ["*.tmp", "*.temp", "*.bak", "*.backup"]
        for pattern in temp_patterns:
            temp_files = list(Path(self.project_root).glob(f"**/{pattern}"))
            if temp_files:
                self.findings.append(SecurityFinding(
                    severity="low",
                    category="file_system",
                    title="Temporary Files Found",
                    description=f"Found {len(temp_files)} temporary files that may contain sensitive data",
                    location=self.project_root,
                    recommendation="Remove temporary files and implement secure cleanup procedures"
                ))
    
    def _check_configuration_security(self):
        """Check configuration security"""
        print("  🔍 Checking configuration security...")
        
        # Check config.yaml security
        config_yaml = os.path.join(self.aicleaner_dir, "config.yaml")
        if os.path.exists(config_yaml):
            try:
                with open(config_yaml, 'r') as f:
                    content = f.read()
                
                # Check for security-related configurations
                security_configs = {
                    "ssl": "SSL/TLS configuration",
                    "auth": "Authentication configuration", 
                    "security": "Security settings",
                    "firewall": "Firewall configuration"
                }
                
                missing_security = []
                for config_key, description in security_configs.items():
                    if config_key not in content.lower():
                        missing_security.append(description)
                
                if missing_security:
                    self.findings.append(SecurityFinding(
                        severity="low",
                        category="config",
                        title="Missing Security Configurations",
                        description=f"Configuration lacks: {', '.join(missing_security)}",
                        location="config.yaml",
                        recommendation="Consider adding security-related configuration options"
                    ))
                    
            except Exception:
                pass
        
        # Check for debug mode in production
        python_files = list(Path(self.aicleaner_dir).glob("**/*.py"))
        for file_path in python_files:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                if re.search(r"debug\s*=\s*True", content, re.IGNORECASE):
                    self.findings.append(SecurityFinding(
                        severity="medium",
                        category="config",
                        title="Debug Mode Enabled",
                        description=f"Debug mode appears to be enabled in {file_path.name}",
                        location=str(file_path),
                        recommendation="Disable debug mode in production"
                    ))
                    
            except Exception:
                pass
    
    def _check_dependency_vulnerabilities(self):
        """Check for known vulnerabilities in dependencies"""
        print("  🔍 Checking dependency vulnerabilities...")
        
        requirements_file = os.path.join(self.aicleaner_dir, "requirements.txt")
        if os.path.exists(requirements_file):
            try:
                with open(requirements_file, 'r') as f:
                    requirements = f.read()
                
                # Check for pinned versions
                unpinned_deps = []
                for line in requirements.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '==' not in line and '>=' not in line and '<=' not in line:
                            unpinned_deps.append(line)
                
                if unpinned_deps:
                    self.findings.append(SecurityFinding(
                        severity="medium",
                        category="dependencies",
                        title="Unpinned Dependencies",
                        description=f"Dependencies without version pins: {', '.join(unpinned_deps)}",
                        location="requirements.txt",
                        recommendation="Pin dependency versions to prevent supply chain attacks"
                    ))
                
                # Check for known vulnerable packages (basic check)
                vulnerable_patterns = [
                    r"requests\s*==\s*2\.25\.[0-2]",  # Example vulnerable version
                    r"urllib3\s*==\s*1\.26\.[0-4]"   # Example vulnerable version
                ]
                
                for pattern in vulnerable_patterns:
                    if re.search(pattern, requirements):
                        self.findings.append(SecurityFinding(
                            severity="high",
                            category="dependencies",
                            title="Potentially Vulnerable Dependency",
                            description=f"Dependency version may have known vulnerabilities",
                            location="requirements.txt",
                            recommendation="Update to latest secure version"
                        ))
                        
            except Exception:
                pass
    
    def _check_api_security(self):
        """Check API security implementation"""
        print("  🔍 Checking API security...")
        
        # Check for rate limiting
        api_files = list(Path(self.aicleaner_dir).glob("**/*api*.py"))
        api_files.extend(list(Path(self.aicleaner_dir).glob("**/*server*.py")))
        
        has_rate_limiting = False
        has_input_validation = False
        
        for file_path in api_files:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                if re.search(r"rate.?limit|throttle", content, re.IGNORECASE):
                    has_rate_limiting = True
                
                if re.search(r"validate|sanitize|escape", content, re.IGNORECASE):
                    has_input_validation = True
                    
            except Exception:
                pass
        
        if api_files and not has_rate_limiting:
            self.findings.append(SecurityFinding(
                severity="medium",
                category="api_security",
                title="Missing Rate Limiting",
                description="API endpoints lack rate limiting protection",
                location="API files",
                recommendation="Implement rate limiting to prevent abuse"
            ))
        
        if api_files and not has_input_validation:
            self.findings.append(SecurityFinding(
                severity="high",
                category="api_security",
                title="Missing API Input Validation",
                description="API endpoints lack input validation",
                location="API files",
                recommendation="Implement comprehensive input validation for all API endpoints"
            ))
    
    def _check_cryptographic_implementation(self):
        """Check cryptographic implementations"""
        print("  🔍 Checking cryptographic implementation...")
        
        python_files = list(Path(self.aicleaner_dir).glob("**/*.py"))
        
        for file_path in python_files:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Check for weak cryptographic practices
                weak_crypto_patterns = [
                    (r"md5\(", "MD5 is cryptographically broken"),
                    (r"sha1\(", "SHA1 is cryptographically weak"),
                    (r"random\.random\(", "Use cryptographically secure random for security purposes"),
                    (r"DES|3DES", "DES/3DES are deprecated encryption algorithms")
                ]
                
                for pattern, description in weak_crypto_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        self.findings.append(SecurityFinding(
                            severity="medium",
                            category="crypto",
                            title="Weak Cryptographic Implementation",
                            description=description,
                            location=str(file_path),
                            recommendation="Use modern, secure cryptographic algorithms"
                        ))
                        
            except Exception:
                pass
    
    def _check_production_hardening(self):
        """Check production hardening measures"""
        print("  🔍 Checking production hardening...")
        
        # Check for security headers in web components
        web_files = list(Path(self.aicleaner_dir).glob("**/*server*.py"))
        web_files.extend(list(Path(self.aicleaner_dir).glob("**/*web*.py")))
        
        security_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy"
        ]
        
        missing_headers = security_headers.copy()
        
        for file_path in web_files:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                for header in security_headers:
                    if header in content:
                        missing_headers.remove(header)
                        
            except Exception:
                pass
        
        if web_files and missing_headers:
            self.findings.append(SecurityFinding(
                severity="medium",
                category="hardening",
                title="Missing Security Headers",
                description=f"Web components lack security headers: {', '.join(missing_headers)}",
                location="Web components",
                recommendation="Implement security headers for web endpoints"
            ))
        
        # Check for error handling that might leak information
        for file_path in Path(self.aicleaner_dir).glob("**/*.py"):
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Look for bare except clauses that might expose stack traces
                if re.search(r"except\s*:", content):
                    self.findings.append(SecurityFinding(
                        severity="low",
                        category="hardening",
                        title="Broad Exception Handling",
                        description=f"File {file_path.name} uses broad exception handling",
                        location=str(file_path),
                        recommendation="Use specific exception handling to prevent information leakage"
                    ))
                    
            except Exception:
                pass

    def _generate_security_report(self) -> SecurityReport:
        """Generate comprehensive security report"""

        # Calculate severity counts
        severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
        for finding in self.findings:
            severity_counts[finding.severity] += 1

        # Calculate overall security score (0-100)
        score = 100
        score -= severity_counts["critical"] * 25  # Critical: -25 points each
        score -= severity_counts["high"] * 15      # High: -15 points each
        score -= severity_counts["medium"] * 8     # Medium: -8 points each
        score -= severity_counts["low"] * 3       # Low: -3 points each
        score -= severity_counts["info"] * 1      # Info: -1 point each

        overall_score = max(0, score)

        # Determine risk level
        if overall_score >= 90:
            risk_level = "low"
        elif overall_score >= 70:
            risk_level = "medium"
        elif overall_score >= 50:
            risk_level = "high"
        else:
            risk_level = "critical"

        # Generate recommendations
        recommendations = self._generate_security_recommendations()

        # Check compliance status
        compliance_status = self._check_compliance_status()

        return SecurityReport(
            timestamp=datetime.now(timezone.utc).isoformat(),
            overall_score=overall_score,
            risk_level=risk_level,
            findings=self.findings,
            summary=severity_counts,
            recommendations=recommendations,
            compliance_status=compliance_status
        )

    def _generate_security_recommendations(self) -> List[str]:
        """Generate prioritized security recommendations"""
        recommendations = []

        # Group findings by category
        category_counts = {}
        for finding in self.findings:
            category_counts[finding.category] = category_counts.get(finding.category, 0) + 1

        # Generate category-specific recommendations
        if category_counts.get("authentication", 0) > 0:
            recommendations.append("Strengthen authentication mechanisms and API key management")

        if category_counts.get("input_validation", 0) > 0:
            recommendations.append("Implement comprehensive input validation and sanitization")

        if category_counts.get("file_system", 0) > 0:
            recommendations.append("Review and secure file system permissions")

        if category_counts.get("dependencies", 0) > 0:
            recommendations.append("Update dependencies and implement vulnerability scanning")

        if category_counts.get("api_security", 0) > 0:
            recommendations.append("Enhance API security with rate limiting and validation")

        if category_counts.get("crypto", 0) > 0:
            recommendations.append("Upgrade cryptographic implementations to modern standards")

        if category_counts.get("hardening", 0) > 0:
            recommendations.append("Implement production hardening measures")

        # Add general recommendations
        recommendations.extend([
            "Implement security monitoring and alerting",
            "Conduct regular security assessments",
            "Establish incident response procedures",
            "Implement security training for development team"
        ])

        return recommendations[:10]  # Top 10 recommendations

    def _check_compliance_status(self) -> Dict[str, bool]:
        """Check compliance with security standards"""
        compliance = {
            "input_validation": True,
            "authentication": True,
            "authorization": True,
            "data_protection": True,
            "logging_monitoring": True,
            "error_handling": True,
            "secure_communication": True,
            "dependency_management": True
        }

        # Check compliance based on findings
        for finding in self.findings:
            if finding.severity in ["critical", "high"]:
                if finding.category == "input_validation":
                    compliance["input_validation"] = False
                elif finding.category == "authentication":
                    compliance["authentication"] = False
                elif finding.category == "api_security":
                    compliance["authorization"] = False
                elif finding.category == "file_system":
                    compliance["data_protection"] = False
                elif finding.category == "dependencies":
                    compliance["dependency_management"] = False

        return compliance

    def _save_security_report(self, report: SecurityReport):
        """Save security report to file"""
        try:
            reports_dir = os.path.join(self.project_root, "security_reports")
            os.makedirs(reports_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = os.path.join(reports_dir, f"security_audit_{timestamp}.json")

            # Convert report to dict for JSON serialization
            report_dict = asdict(report)

            with open(report_file, 'w') as f:
                json.dump(report_dict, f, indent=2, default=str)

            print(f"🔒 Security report saved to {report_file}")

        except Exception as e:
            print(f"❌ Failed to save security report: {e}")

    def run_quick_security_check(self) -> Dict[str, Any]:
        """Run quick security check (essential checks only)"""
        print("🔒 Running quick security check...")

        # Clear previous findings
        self.findings = []

        # Run essential checks
        self._check_authentication_security()
        self._check_configuration_security()
        self._check_file_system_security()

        # Generate quick report
        severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
        for finding in self.findings:
            severity_counts[finding.severity] += 1

        # Calculate quick score
        score = 100
        score -= severity_counts["critical"] * 25
        score -= severity_counts["high"] * 15
        score -= severity_counts["medium"] * 8

        quick_score = max(0, score)

        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "quick_security_score": quick_score,
            "critical_issues": severity_counts["critical"],
            "high_issues": severity_counts["high"],
            "medium_issues": severity_counts["medium"],
            "total_findings": len(self.findings),
            "risk_level": "low" if quick_score >= 80 else "medium" if quick_score >= 60 else "high",
            "top_recommendations": self._generate_security_recommendations()[:3]
        }
