"""
Performance Metrics Collection System for AICleaner
Tracks and analyzes performance metrics for AI analysis, caching, API calls, and system performance
"""

import os
import json
import time
import threading
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import statistics


@dataclass
class MetricPoint:
    """Individual metric data point"""
    timestamp: str
    value: float
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class PerformanceStats:
    """Performance statistics for a metric"""
    count: int
    min_value: float
    max_value: float
    avg_value: float
    median_value: float
    p95_value: float
    p99_value: float
    total_value: float
    last_value: float
    trend: str  # "improving", "degrading", "stable"


class MetricsCollector:
    """
    Comprehensive performance metrics collection and analysis system
    
    Features:
    - Real-time metrics collection
    - Statistical analysis and trending
    - Performance alerting
    - Historical data retention
    - Metric aggregation and reporting
    """
    
    def __init__(self, data_dir: str = "/data", retention_hours: int = 168):
        """
        Initialize metrics collector
        
        Args:
            data_dir: Directory for storing metrics data
            retention_hours: How long to retain detailed metrics (default: 7 days)
        """
        self.data_dir = data_dir
        self.metrics_dir = os.path.join(data_dir, "metrics")
        self.retention_hours = retention_hours
        
        # Ensure metrics directory exists
        os.makedirs(self.metrics_dir, exist_ok=True)
        
        # In-memory metrics storage for fast access
        self.metrics = defaultdict(lambda: deque(maxlen=10000))
        self.metric_locks = defaultdict(threading.Lock)
        
        # Performance thresholds for alerting
        self.thresholds = {
            "ai_analysis_time": {"warning": 5.0, "critical": 10.0},
            "cache_hit_rate": {"warning": 0.7, "critical": 0.5},
            "memory_usage_mb": {"warning": 300, "critical": 500},
            "api_response_time": {"warning": 2.0, "critical": 5.0},
            "error_rate": {"warning": 0.05, "critical": 0.1}
        }
        
        # Aggregated metrics for reporting
        self.hourly_aggregates = defaultdict(dict)
        self.daily_aggregates = defaultdict(dict)
        
        # Load existing metrics
        self._load_persisted_metrics()
        
        # Start background cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_old_metrics, daemon=True)
        self.cleanup_thread.start()
    
    def record_metric(self, metric_name: str, value: float, metadata: Dict[str, Any] = None):
        """
        Record a metric value
        
        Args:
            metric_name: Name of the metric
            value: Metric value
            metadata: Additional metadata for the metric
        """
        timestamp = datetime.now(timezone.utc).isoformat()
        
        metric_point = MetricPoint(
            timestamp=timestamp,
            value=value,
            metadata=metadata or {}
        )
        
        with self.metric_locks[metric_name]:
            self.metrics[metric_name].append(metric_point)
        
        # Check for threshold violations
        self._check_thresholds(metric_name, value)
        
        # Update aggregates
        self._update_aggregates(metric_name, value, timestamp)
    
    def record_ai_analysis(self, zone_name: str, analysis_time: float, cache_hit: bool, 
                          analysis_type: str = "standard"):
        """
        Record AI analysis performance metrics
        
        Args:
            zone_name: Name of the zone analyzed
            analysis_time: Time taken for analysis in seconds
            cache_hit: Whether this was a cache hit
            analysis_type: Type of analysis performed
        """
        metadata = {
            "zone_name": zone_name,
            "cache_hit": cache_hit,
            "analysis_type": analysis_type
        }
        
        self.record_metric("ai_analysis_time", analysis_time, metadata)
        self.record_metric("cache_hit", 1.0 if cache_hit else 0.0, metadata)
        
        # Update cache hit rate
        self._update_cache_hit_rate()
    
    def record_api_call(self, endpoint: str, response_time: float, status_code: int, 
                       error: Optional[str] = None):
        """
        Record API call performance metrics
        
        Args:
            endpoint: API endpoint called
            response_time: Response time in seconds
            status_code: HTTP status code
            error: Error message if any
        """
        metadata = {
            "endpoint": endpoint,
            "status_code": status_code,
            "success": status_code < 400,
            "error": error
        }
        
        self.record_metric("api_response_time", response_time, metadata)
        
        if status_code >= 400:
            self.record_metric("api_error", 1.0, metadata)
        else:
            self.record_metric("api_success", 1.0, metadata)
        
        # Update error rate
        self._update_error_rate()
    
    def record_memory_usage(self, memory_mb: float, component: str = "system"):
        """
        Record memory usage metrics
        
        Args:
            memory_mb: Memory usage in megabytes
            component: Component name for the memory usage
        """
        metadata = {"component": component}
        self.record_metric("memory_usage_mb", memory_mb, metadata)
    
    def record_cache_operation(self, operation: str, hit: bool, size_bytes: int = 0):
        """
        Record cache operation metrics
        
        Args:
            operation: Cache operation type (get, set, delete)
            hit: Whether operation was a hit
            size_bytes: Size of cached data in bytes
        """
        metadata = {
            "operation": operation,
            "size_bytes": size_bytes
        }
        
        self.record_metric("cache_operation", 1.0, metadata)
        if operation == "get":
            self.record_metric("cache_hit", 1.0 if hit else 0.0, metadata)
    
    def get_metric_stats(self, metric_name: str, hours: int = 24) -> Optional[PerformanceStats]:
        """
        Get statistical analysis for a metric
        
        Args:
            metric_name: Name of the metric
            hours: Number of hours to analyze
            
        Returns:
            PerformanceStats object or None if no data
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        with self.metric_locks[metric_name]:
            if metric_name not in self.metrics:
                return None
            
            # Filter metrics within time range
            recent_metrics = [
                m for m in self.metrics[metric_name]
                if datetime.fromisoformat(m.timestamp.replace('Z', '+00:00')) >= cutoff_time
            ]
            
            if not recent_metrics:
                return None
            
            values = [m.value for m in recent_metrics]
            
            # Calculate statistics
            count = len(values)
            min_value = min(values)
            max_value = max(values)
            avg_value = statistics.mean(values)
            median_value = statistics.median(values)
            
            # Calculate percentiles
            sorted_values = sorted(values)
            p95_index = int(0.95 * len(sorted_values))
            p99_index = int(0.99 * len(sorted_values))
            p95_value = sorted_values[min(p95_index, len(sorted_values) - 1)]
            p99_value = sorted_values[min(p99_index, len(sorted_values) - 1)]
            
            total_value = sum(values)
            last_value = values[-1]
            
            # Calculate trend
            trend = self._calculate_trend(values)
            
            return PerformanceStats(
                count=count,
                min_value=min_value,
                max_value=max_value,
                avg_value=avg_value,
                median_value=median_value,
                p95_value=p95_value,
                p99_value=p99_value,
                total_value=total_value,
                last_value=last_value,
                trend=trend
            )
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get comprehensive performance summary
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Dictionary with performance summary
        """
        summary = {
            "period_hours": hours,
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "metrics": {}
        }
        
        # Key metrics to include in summary
        key_metrics = [
            "ai_analysis_time",
            "cache_hit_rate",
            "memory_usage_mb",
            "api_response_time",
            "error_rate"
        ]
        
        for metric_name in key_metrics:
            stats = self.get_metric_stats(metric_name, hours)
            if stats:
                summary["metrics"][metric_name] = asdict(stats)
        
        # Add derived metrics
        summary["derived_metrics"] = self._calculate_derived_metrics(hours)
        
        # Add performance alerts
        summary["alerts"] = self._get_performance_alerts(hours)
        
        return summary
    
    def _update_cache_hit_rate(self):
        """Update cache hit rate metric"""
        with self.metric_locks["cache_hit"]:
            if "cache_hit" not in self.metrics:
                return
            
            # Calculate hit rate from last 100 cache operations
            recent_hits = list(self.metrics["cache_hit"])[-100:]
            if recent_hits:
                hit_rate = sum(m.value for m in recent_hits) / len(recent_hits)
                self.record_metric("cache_hit_rate", hit_rate)
    
    def _update_error_rate(self):
        """Update error rate metric"""
        with self.metric_locks["api_error"]:
            with self.metric_locks["api_success"]:
                # Calculate error rate from recent API calls
                recent_errors = list(self.metrics["api_error"])[-100:]
                recent_successes = list(self.metrics["api_success"])[-100:]
                
                total_calls = len(recent_errors) + len(recent_successes)
                if total_calls > 0:
                    error_rate = len(recent_errors) / total_calls
                    self.record_metric("error_rate", error_rate)
    
    def _calculate_trend(self, values: List[float]) -> str:
        """
        Calculate trend direction for a series of values
        
        Args:
            values: List of metric values
            
        Returns:
            Trend direction: "improving", "degrading", or "stable"
        """
        if len(values) < 10:
            return "stable"
        
        # Split into two halves and compare averages
        mid_point = len(values) // 2
        first_half_avg = statistics.mean(values[:mid_point])
        second_half_avg = statistics.mean(values[mid_point:])
        
        # Calculate percentage change
        if first_half_avg == 0:
            return "stable"
        
        change_percent = (second_half_avg - first_half_avg) / first_half_avg
        
        # For metrics where lower is better (like response time, error rate)
        lower_is_better = ["ai_analysis_time", "api_response_time", "error_rate", "memory_usage_mb"]
        
        if abs(change_percent) < 0.05:  # Less than 5% change
            return "stable"
        elif change_percent < 0:
            return "improving" if any(metric in str(values) for metric in lower_is_better) else "degrading"
        else:
            return "degrading" if any(metric in str(values) for metric in lower_is_better) else "improving"
    
    def _check_thresholds(self, metric_name: str, value: float):
        """Check if metric value exceeds thresholds"""
        if metric_name not in self.thresholds:
            return
        
        thresholds = self.thresholds[metric_name]
        
        if value >= thresholds.get("critical", float('inf')):
            self._record_alert("critical", metric_name, value, thresholds["critical"])
        elif value >= thresholds.get("warning", float('inf')):
            self._record_alert("warning", metric_name, value, thresholds["warning"])
    
    def _record_alert(self, level: str, metric_name: str, value: float, threshold: float):
        """Record a performance alert"""
        alert = {
            "level": level,
            "metric": metric_name,
            "value": value,
            "threshold": threshold,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        # Store alert
        alerts_file = os.path.join(self.metrics_dir, "alerts.json")
        alerts = []
        
        if os.path.exists(alerts_file):
            with open(alerts_file, 'r') as f:
                alerts = json.load(f)
        
        alerts.append(alert)
        
        # Keep only last 1000 alerts
        alerts = alerts[-1000:]
        
        with open(alerts_file, 'w') as f:
            json.dump(alerts, f, indent=2)
    
    def _get_performance_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get performance alerts from the specified time period"""
        alerts_file = os.path.join(self.metrics_dir, "alerts.json")
        
        if not os.path.exists(alerts_file):
            return []
        
        try:
            with open(alerts_file, 'r') as f:
                all_alerts = json.load(f)
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            recent_alerts = [
                alert for alert in all_alerts
                if datetime.fromisoformat(alert["timestamp"].replace('Z', '+00:00')) >= cutoff_time
            ]
            
            return recent_alerts
            
        except Exception:
            return []
    
    def _calculate_derived_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """Calculate derived performance metrics"""
        derived = {}
        
        # AI Analysis efficiency
        ai_stats = self.get_metric_stats("ai_analysis_time", hours)
        cache_stats = self.get_metric_stats("cache_hit_rate", hours)
        
        if ai_stats and cache_stats:
            derived["ai_efficiency_score"] = min(100, max(0, 
                100 - (ai_stats.avg_value * 10) + (cache_stats.avg_value * 50)
            ))
        
        # System performance score
        memory_stats = self.get_metric_stats("memory_usage_mb", hours)
        api_stats = self.get_metric_stats("api_response_time", hours)
        
        if memory_stats and api_stats:
            memory_score = max(0, 100 - (memory_stats.avg_value / 5))  # 500MB = 0 score
            api_score = max(0, 100 - (api_stats.avg_value * 20))  # 5s = 0 score
            derived["system_performance_score"] = (memory_score + api_score) / 2
        
        # Reliability score
        error_stats = self.get_metric_stats("error_rate", hours)
        if error_stats:
            derived["reliability_score"] = max(0, 100 - (error_stats.avg_value * 1000))
        
        return derived
    
    def _update_aggregates(self, metric_name: str, value: float, timestamp: str):
        """Update hourly and daily aggregates"""
        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        hour_key = dt.strftime("%Y-%m-%d-%H")
        day_key = dt.strftime("%Y-%m-%d")
        
        # Update hourly aggregates
        if hour_key not in self.hourly_aggregates[metric_name]:
            self.hourly_aggregates[metric_name][hour_key] = {
                "values": [],
                "count": 0,
                "sum": 0.0,
                "min": float('inf'),
                "max": float('-inf')
            }
        
        agg = self.hourly_aggregates[metric_name][hour_key]
        agg["values"].append(value)
        agg["count"] += 1
        agg["sum"] += value
        agg["min"] = min(agg["min"], value)
        agg["max"] = max(agg["max"], value)
        
        # Update daily aggregates similarly
        if day_key not in self.daily_aggregates[metric_name]:
            self.daily_aggregates[metric_name][day_key] = {
                "values": [],
                "count": 0,
                "sum": 0.0,
                "min": float('inf'),
                "max": float('-inf')
            }
        
        daily_agg = self.daily_aggregates[metric_name][day_key]
        daily_agg["values"].append(value)
        daily_agg["count"] += 1
        daily_agg["sum"] += value
        daily_agg["min"] = min(daily_agg["min"], value)
        daily_agg["max"] = max(daily_agg["max"], value)
    
    def _load_persisted_metrics(self):
        """Load persisted metrics from disk"""
        try:
            metrics_file = os.path.join(self.metrics_dir, "persisted_metrics.json")
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    data = json.load(f)
                
                # Load recent metrics into memory
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=1)
                
                for metric_name, points in data.items():
                    for point_data in points:
                        point_time = datetime.fromisoformat(point_data["timestamp"].replace('Z', '+00:00'))
                        if point_time >= cutoff_time:
                            point = MetricPoint(**point_data)
                            self.metrics[metric_name].append(point)
                            
        except Exception as e:
            print(f"Error loading persisted metrics: {e}")
    
    def _cleanup_old_metrics(self):
        """Background thread to clean up old metrics"""
        while True:
            try:
                time.sleep(3600)  # Run every hour
                
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=self.retention_hours)
                
                # Clean up in-memory metrics
                for metric_name in list(self.metrics.keys()):
                    with self.metric_locks[metric_name]:
                        original_count = len(self.metrics[metric_name])
                        self.metrics[metric_name] = deque([
                            m for m in self.metrics[metric_name]
                            if datetime.fromisoformat(m.timestamp.replace('Z', '+00:00')) >= cutoff_time
                        ], maxlen=10000)
                        
                        cleaned_count = original_count - len(self.metrics[metric_name])
                        if cleaned_count > 0:
                            print(f"Cleaned {cleaned_count} old metrics for {metric_name}")
                
                # Clean up aggregates
                self._cleanup_old_aggregates(cutoff_time)
                
            except Exception as e:
                print(f"Error in metrics cleanup: {e}")
    
    def _cleanup_old_aggregates(self, cutoff_time: datetime):
        """Clean up old aggregate data"""
        cutoff_hour = cutoff_time.strftime("%Y-%m-%d-%H")
        cutoff_day = cutoff_time.strftime("%Y-%m-%d")
        
        # Clean hourly aggregates
        for metric_name in list(self.hourly_aggregates.keys()):
            old_keys = [k for k in self.hourly_aggregates[metric_name].keys() if k < cutoff_hour]
            for key in old_keys:
                del self.hourly_aggregates[metric_name][key]
        
        # Clean daily aggregates (keep longer)
        daily_cutoff = cutoff_time - timedelta(days=30)
        daily_cutoff_str = daily_cutoff.strftime("%Y-%m-%d")
        
        for metric_name in list(self.daily_aggregates.keys()):
            old_keys = [k for k in self.daily_aggregates[metric_name].keys() if k < daily_cutoff_str]
            for key in old_keys:
                del self.daily_aggregates[metric_name][key]
    
    def export_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """
        Export metrics data for external analysis
        
        Args:
            hours: Number of hours of data to export
            
        Returns:
            Dictionary with exported metrics data
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        exported_data = {
            "export_timestamp": datetime.now(timezone.utc).isoformat(),
            "period_hours": hours,
            "metrics": {}
        }
        
        for metric_name, metric_data in self.metrics.items():
            with self.metric_locks[metric_name]:
                recent_data = [
                    asdict(m) for m in metric_data
                    if datetime.fromisoformat(m.timestamp.replace('Z', '+00:00')) >= cutoff_time
                ]
                
                if recent_data:
                    exported_data["metrics"][metric_name] = recent_data
        
        return exported_data
