"""
Predictive Analytics System for AICleaner
Analyzes historical patterns and provides predictive cleaning recommendations
"""
import os
import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, Counter
import statistics
from dataclasses import dataclass, asdict
from enum import Enum


class CleaningPattern(Enum):
    """Types of cleaning patterns"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    SEASONAL = "seasonal"
    IRREGULAR = "irregular"


class TaskCategory(Enum):
    """Categories of cleaning tasks"""
    CLEANING = "cleaning"
    ORGANIZATION = "organization"
    MAINTENANCE = "maintenance"
    DEEP_CLEANING = "deep_cleaning"
    SEASONAL = "seasonal"


@dataclass
class PredictiveInsight:
    """Represents a predictive insight about cleaning patterns"""
    zone_name: str
    insight_type: str
    confidence: float
    description: str
    recommendation: str
    next_predicted_date: Optional[str] = None
    supporting_data: Optional[Dict] = None


@dataclass
class CleaningTrend:
    """Represents a cleaning trend analysis"""
    zone_name: str
    task_category: TaskCategory
    frequency_days: float
    trend_direction: str  # "increasing", "decreasing", "stable"
    confidence: float
    last_occurrence: str
    predicted_next: str


class PredictiveAnalytics:
    """
    Predictive analytics engine for AICleaner
    
    Features:
    - Historical pattern analysis
    - Task frequency prediction
    - Optimal cleaning schedule recommendations
    - Seasonal adjustment predictions
    - Usage pattern recognition
    """
    
    def __init__(self, data_path: str = "/data/analytics"):
        """
        Initialize predictive analytics system
        
        Args:
            data_path: Path to store analytics data
        """
        self.data_path = data_path
        self.logger = logging.getLogger(__name__)
        
        # Ensure data directory exists
        os.makedirs(data_path, exist_ok=True)
        
        # Analytics data storage
        self.historical_data = self._load_historical_data()
        self.patterns = {}
        self.trends = {}
        
        self.logger.info("Predictive Analytics system initialized")
    
    def _load_historical_data(self) -> Dict:
        """Load historical analytics data"""
        data_file = os.path.join(self.data_path, "historical_data.json")
        
        if os.path.exists(data_file):
            try:
                with open(data_file, 'r') as f:
                    data = json.load(f)
                self.logger.info(f"Loaded historical data with {len(data)} zones")
                return data
            except Exception as e:
                self.logger.error(f"Error loading historical data: {e}")
        
        return {}
    
    def _save_historical_data(self):
        """Save historical analytics data"""
        data_file = os.path.join(self.data_path, "historical_data.json")
        
        try:
            with open(data_file, 'w') as f:
                json.dump(self.historical_data, f, indent=2)
            self.logger.debug("Historical data saved successfully")
        except Exception as e:
            self.logger.error(f"Error saving historical data: {e}")
    
    def record_task_completion(self, zone_name: str, task_description: str, 
                             completion_time: datetime, task_priority: int = 5):
        """
        Record a completed task for pattern analysis
        
        Args:
            zone_name: Name of the zone
            task_description: Description of the completed task
            completion_time: When the task was completed
            task_priority: Priority level of the task (1-10)
        """
        if zone_name not in self.historical_data:
            self.historical_data[zone_name] = {
                'tasks': [],
                'patterns': {},
                'last_updated': datetime.now(timezone.utc).isoformat()
            }
        
        # Categorize the task
        task_category = self._categorize_task(task_description)
        
        # Record the task
        task_record = {
            'description': task_description,
            'category': task_category.value,
            'completion_time': completion_time.isoformat(),
            'priority': task_priority,
            'day_of_week': completion_time.weekday(),
            'hour_of_day': completion_time.hour,
            'month': completion_time.month
        }
        
        self.historical_data[zone_name]['tasks'].append(task_record)
        self.historical_data[zone_name]['last_updated'] = datetime.now(timezone.utc).isoformat()
        
        # Limit historical data to last 1000 tasks per zone
        if len(self.historical_data[zone_name]['tasks']) > 1000:
            self.historical_data[zone_name]['tasks'] = self.historical_data[zone_name]['tasks'][-1000:]
        
        self._save_historical_data()
        self.logger.debug(f"Recorded task completion for {zone_name}: {task_description}")
    
    def _categorize_task(self, task_description: str) -> TaskCategory:
        """Categorize a task based on its description"""
        description_lower = task_description.lower()
        
        # Deep cleaning keywords
        if any(keyword in description_lower for keyword in 
               ['deep clean', 'scrub', 'polish', 'detail', 'thorough']):
            return TaskCategory.DEEP_CLEANING
        
        # Organization keywords
        elif any(keyword in description_lower for keyword in 
                ['organize', 'sort', 'arrange', 'tidy', 'declutter', 'put away']):
            return TaskCategory.ORGANIZATION
        
        # Maintenance keywords
        elif any(keyword in description_lower for keyword in 
                ['fix', 'repair', 'replace', 'maintain', 'check', 'inspect']):
            return TaskCategory.MAINTENANCE
        
        # Seasonal keywords
        elif any(keyword in description_lower for keyword in 
                ['seasonal', 'holiday', 'winter', 'summer', 'spring', 'fall']):
            return TaskCategory.SEASONAL
        
        # Default to cleaning
        else:
            return TaskCategory.CLEANING
    
    def analyze_patterns(self, zone_name: str) -> List[CleaningTrend]:
        """
        Analyze cleaning patterns for a specific zone
        
        Args:
            zone_name: Name of the zone to analyze
            
        Returns:
            List of cleaning trends
        """
        if zone_name not in self.historical_data:
            return []
        
        tasks = self.historical_data[zone_name]['tasks']
        if len(tasks) < 3:  # Need at least 3 tasks for pattern analysis
            return []
        
        trends = []
        
        # Group tasks by category
        tasks_by_category = defaultdict(list)
        for task in tasks:
            category = TaskCategory(task['category'])
            completion_time = datetime.fromisoformat(task['completion_time'])
            tasks_by_category[category].append(completion_time)
        
        # Analyze each category
        for category, completion_times in tasks_by_category.items():
            if len(completion_times) < 2:
                continue
            
            # Sort by time
            completion_times.sort()
            
            # Calculate intervals between tasks
            intervals = []
            for i in range(1, len(completion_times)):
                interval = (completion_times[i] - completion_times[i-1]).days
                intervals.append(interval)
            
            if not intervals:
                continue
            
            # Calculate average frequency
            avg_frequency = statistics.mean(intervals)
            
            # Determine trend direction
            if len(intervals) >= 3:
                recent_avg = statistics.mean(intervals[-3:])
                older_avg = statistics.mean(intervals[:-3]) if len(intervals) > 3 else avg_frequency
                
                if recent_avg < older_avg * 0.8:
                    trend_direction = "increasing"  # More frequent
                elif recent_avg > older_avg * 1.2:
                    trend_direction = "decreasing"  # Less frequent
                else:
                    trend_direction = "stable"
            else:
                trend_direction = "stable"
            
            # Calculate confidence based on consistency
            if len(intervals) > 1:
                std_dev = statistics.stdev(intervals)
                confidence = max(0.1, min(1.0, 1.0 - (std_dev / avg_frequency)))
            else:
                confidence = 0.5
            
            # Predict next occurrence
            last_completion = completion_times[-1]
            predicted_next = last_completion + timedelta(days=avg_frequency)
            
            trend = CleaningTrend(
                zone_name=zone_name,
                task_category=category,
                frequency_days=avg_frequency,
                trend_direction=trend_direction,
                confidence=confidence,
                last_occurrence=last_completion.isoformat(),
                predicted_next=predicted_next.isoformat()
            )
            
            trends.append(trend)
        
        return trends
    
    def generate_predictive_insights(self, zone_name: str) -> List[PredictiveInsight]:
        """
        Generate predictive insights for a zone
        
        Args:
            zone_name: Name of the zone
            
        Returns:
            List of predictive insights
        """
        insights = []
        trends = self.analyze_patterns(zone_name)
        
        if not trends:
            return insights
        
        # Insight 1: Overdue tasks
        now = datetime.now(timezone.utc)
        for trend in trends:
            predicted_date = datetime.fromisoformat(trend.predicted_next.replace('Z', '+00:00'))
            if predicted_date < now:
                days_overdue = (now - predicted_date).days
                
                insight = PredictiveInsight(
                    zone_name=zone_name,
                    insight_type="overdue_task",
                    confidence=trend.confidence,
                    description=f"{trend.task_category.value.title()} tasks are {days_overdue} days overdue",
                    recommendation=f"Schedule {trend.task_category.value} tasks soon to maintain routine",
                    next_predicted_date=trend.predicted_next,
                    supporting_data=asdict(trend)
                )
                insights.append(insight)
        
        # Insight 2: Optimal scheduling
        for trend in trends:
            if trend.confidence > 0.7:
                insight = PredictiveInsight(
                    zone_name=zone_name,
                    insight_type="optimal_schedule",
                    confidence=trend.confidence,
                    description=f"{trend.task_category.value.title()} tasks occur every {trend.frequency_days:.1f} days",
                    recommendation=f"Schedule {trend.task_category.value} tasks every {int(trend.frequency_days)} days for optimal maintenance",
                    next_predicted_date=trend.predicted_next,
                    supporting_data=asdict(trend)
                )
                insights.append(insight)
        
        # Insight 3: Trend analysis
        increasing_trends = [t for t in trends if t.trend_direction == "increasing"]
        if increasing_trends:
            categories = [t.task_category.value for t in increasing_trends]
            insight = PredictiveInsight(
                zone_name=zone_name,
                insight_type="increasing_frequency",
                confidence=statistics.mean([t.confidence for t in increasing_trends]),
                description=f"Increasing frequency in: {', '.join(categories)}",
                recommendation="Consider if current cleaning routine needs adjustment",
                supporting_data={'categories': categories}
            )
            insights.append(insight)
        
        return insights

    def get_optimal_schedule(self, zone_name: str, days_ahead: int = 30) -> Dict[str, List[str]]:
        """
        Generate optimal cleaning schedule for the next N days

        Args:
            zone_name: Name of the zone
            days_ahead: Number of days to schedule ahead

        Returns:
            Dictionary with dates as keys and list of recommended tasks as values
        """
        schedule = defaultdict(list)
        trends = self.analyze_patterns(zone_name)

        if not trends:
            return {}

        now = datetime.now(timezone.utc)

        for trend in trends:
            if trend.confidence < 0.5:  # Skip low-confidence trends
                continue

            if trend.frequency_days <= 0:  # Skip invalid frequencies
                continue

            predicted_date = datetime.fromisoformat(trend.predicted_next.replace('Z', '+00:00'))

            # Schedule tasks within the specified timeframe
            current_date = predicted_date
            iterations = 0
            max_iterations = 100  # Prevent infinite loops

            while current_date <= now + timedelta(days=days_ahead) and iterations < max_iterations:
                date_str = current_date.strftime('%Y-%m-%d')
                task_desc = f"{trend.task_category.value.title()} tasks (predicted)"
                schedule[date_str].append(task_desc)

                # Next occurrence
                current_date += timedelta(days=trend.frequency_days)
                iterations += 1

        return dict(schedule)

    def analyze_seasonal_patterns(self, zone_name: str) -> Dict[str, Any]:
        """
        Analyze seasonal cleaning patterns

        Args:
            zone_name: Name of the zone

        Returns:
            Dictionary with seasonal analysis
        """
        if zone_name not in self.historical_data:
            return {}

        tasks = self.historical_data[zone_name]['tasks']
        if len(tasks) < 12:  # Need at least a year of data
            return {'insufficient_data': True}

        # Group tasks by month
        monthly_counts = defaultdict(int)
        monthly_categories = defaultdict(lambda: defaultdict(int))

        for task in tasks:
            completion_time = datetime.fromisoformat(task['completion_time'])
            month = completion_time.month
            category = task['category']

            monthly_counts[month] += 1
            monthly_categories[month][category] += 1

        # Find peak and low months
        peak_month = max(monthly_counts, key=monthly_counts.get)
        low_month = min(monthly_counts, key=monthly_counts.get)

        # Seasonal recommendations
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

        return {
            'peak_month': month_names[peak_month - 1],
            'low_month': month_names[low_month - 1],
            'monthly_activity': {month_names[m-1]: count for m, count in monthly_counts.items()},
            'seasonal_categories': {
                month_names[m-1]: dict(categories)
                for m, categories in monthly_categories.items()
            },
            'recommendations': self._generate_seasonal_recommendations(monthly_counts, monthly_categories)
        }

    def _generate_seasonal_recommendations(self, monthly_counts: Dict, monthly_categories: Dict) -> List[str]:
        """Generate seasonal cleaning recommendations"""
        recommendations = []

        # Find months with high activity
        avg_activity = statistics.mean(monthly_counts.values())
        high_activity_months = [m for m, count in monthly_counts.items() if count > avg_activity * 1.2]

        if high_activity_months:
            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            high_months_str = ', '.join([month_names[m-1] for m in high_activity_months])
            recommendations.append(f"Increase cleaning frequency during {high_months_str}")

        # Find dominant categories by season
        spring_months = [3, 4, 5]  # Mar, Apr, May
        summer_months = [6, 7, 8]  # Jun, Jul, Aug
        fall_months = [9, 10, 11]  # Sep, Oct, Nov
        winter_months = [12, 1, 2]  # Dec, Jan, Feb

        seasons = {
            'Spring': spring_months,
            'Summer': summer_months,
            'Fall': fall_months,
            'Winter': winter_months
        }

        for season, months in seasons.items():
            season_categories = defaultdict(int)
            for month in months:
                if month in monthly_categories:
                    for category, count in monthly_categories[month].items():
                        season_categories[category] += count

            if season_categories:
                top_category = max(season_categories, key=season_categories.get)
                recommendations.append(f"{season}: Focus on {top_category} tasks")

        return recommendations

    def get_efficiency_metrics(self, zone_name: str) -> Dict[str, Any]:
        """
        Calculate efficiency metrics for a zone

        Args:
            zone_name: Name of the zone

        Returns:
            Dictionary with efficiency metrics
        """
        if zone_name not in self.historical_data:
            return {}

        tasks = self.historical_data[zone_name]['tasks']
        if len(tasks) < 5:
            return {'insufficient_data': True}

        # Calculate metrics
        total_tasks = len(tasks)

        # Task completion rate over time
        recent_tasks = [t for t in tasks if
                       datetime.fromisoformat(t['completion_time']) >
                       datetime.now(timezone.utc) - timedelta(days=30)]

        # Priority distribution
        priority_counts = Counter([task['priority'] for task in tasks])
        avg_priority = statistics.mean([task['priority'] for task in tasks])

        # Category distribution
        category_counts = Counter([task['category'] for task in tasks])

        # Time-based patterns
        hour_counts = Counter([task['hour_of_day'] for task in tasks])
        day_counts = Counter([task['day_of_week'] for task in tasks])

        # Most productive time
        peak_hour = max(hour_counts, key=hour_counts.get)
        peak_day = max(day_counts, key=day_counts.get)

        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

        return {
            'total_tasks_completed': total_tasks,
            'recent_tasks_30_days': len(recent_tasks),
            'average_priority': round(avg_priority, 2),
            'priority_distribution': dict(priority_counts),
            'category_distribution': dict(category_counts),
            'peak_completion_hour': peak_hour,
            'peak_completion_day': day_names[peak_day],
            'completion_consistency': self._calculate_consistency(tasks),
            'efficiency_score': self._calculate_efficiency_score(tasks)
        }

    def _calculate_consistency(self, tasks: List[Dict]) -> float:
        """Calculate consistency score based on task completion intervals"""
        if len(tasks) < 3:
            return 0.0

        completion_times = [datetime.fromisoformat(task['completion_time']) for task in tasks]
        completion_times.sort()

        intervals = []
        for i in range(1, len(completion_times)):
            interval = (completion_times[i] - completion_times[i-1]).days
            intervals.append(interval)

        if not intervals:
            return 0.0

        # Lower standard deviation = higher consistency
        try:
            std_dev = statistics.stdev(intervals)
            mean_interval = statistics.mean(intervals)
            consistency = max(0.0, min(1.0, 1.0 - (std_dev / max(mean_interval, 1))))
            return round(consistency, 3)
        except:
            return 0.0

    def _calculate_efficiency_score(self, tasks: List[Dict]) -> float:
        """Calculate overall efficiency score"""
        if len(tasks) < 3:
            return 0.0

        # Factors: consistency, priority balance, recent activity
        consistency = self._calculate_consistency(tasks)

        # Priority balance (prefer mix of priorities)
        priorities = [task['priority'] for task in tasks]
        priority_variety = len(set(priorities)) / 10.0  # Normalize to 0-1

        # Recent activity (more recent activity = higher score)
        now = datetime.now(timezone.utc)
        recent_tasks = [t for t in tasks if
                       datetime.fromisoformat(t['completion_time']) > now - timedelta(days=7)]
        recent_activity = min(1.0, len(recent_tasks) / 7.0)  # Up to 1 task per day

        # Weighted efficiency score
        efficiency = (consistency * 0.4) + (priority_variety * 0.3) + (recent_activity * 0.3)
        return round(efficiency, 3)

    def export_analytics_report(self, zone_name: str) -> Dict[str, Any]:
        """
        Export comprehensive analytics report for a zone

        Args:
            zone_name: Name of the zone

        Returns:
            Complete analytics report
        """
        return {
            'zone_name': zone_name,
            'generated_at': datetime.now(timezone.utc).isoformat(),
            'trends': [asdict(trend) for trend in self.analyze_patterns(zone_name)],
            'insights': [asdict(insight) for insight in self.generate_predictive_insights(zone_name)],
            'seasonal_analysis': self.analyze_seasonal_patterns(zone_name),
            'efficiency_metrics': self.get_efficiency_metrics(zone_name),
            'optimal_schedule': self.get_optimal_schedule(zone_name, 14),  # 2 weeks ahead
            'data_quality': {
                'total_tasks': len(self.historical_data.get(zone_name, {}).get('tasks', [])),
                'date_range': self._get_date_range(zone_name),
                'completeness_score': self._calculate_data_completeness(zone_name)
            }
        }

    def _get_date_range(self, zone_name: str) -> Dict[str, str]:
        """Get the date range of available data"""
        if zone_name not in self.historical_data:
            return {}

        tasks = self.historical_data[zone_name]['tasks']
        if not tasks:
            return {}

        dates = [datetime.fromisoformat(task['completion_time']) for task in tasks]
        return {
            'earliest': min(dates).isoformat(),
            'latest': max(dates).isoformat(),
            'span_days': (max(dates) - min(dates)).days
        }

    def _calculate_data_completeness(self, zone_name: str) -> float:
        """Calculate data completeness score"""
        if zone_name not in self.historical_data:
            return 0.0

        tasks = self.historical_data[zone_name]['tasks']
        if len(tasks) < 10:
            return len(tasks) / 10.0  # Need at least 10 tasks for good analysis

        return min(1.0, len(tasks) / 100.0)  # Optimal at 100+ tasks
