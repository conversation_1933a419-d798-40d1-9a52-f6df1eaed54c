"""
Error Tracking and Alerting System for AICleaner
Provides comprehensive error tracking, categorization, alerting, and recovery mechanisms
"""

import os
import json
import time
import logging
import traceback
import threading
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification"""
    AI_ANALYSIS = "ai_analysis"
    HOME_ASSISTANT = "home_assistant"
    CONFIGURATION = "configuration"
    NETWORK = "network"
    STORAGE = "storage"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    SYSTEM = "system"
    UNKNOWN = "unknown"


@dataclass
class ErrorEvent:
    """Individual error event"""
    id: str
    timestamp: str
    severity: ErrorSeverity
    category: ErrorCategory
    component: str
    message: str
    details: Dict[str, Any]
    stack_trace: Optional[str] = None
    context: Dict[str, Any] = None
    resolved: bool = False
    resolution_notes: Optional[str] = None
    occurrence_count: int = 1
    first_seen: Optional[str] = None
    last_seen: Optional[str] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}
        if self.first_seen is None:
            self.first_seen = self.timestamp
        if self.last_seen is None:
            self.last_seen = self.timestamp


@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    condition: str  # "error_count", "error_rate", "severity_threshold"
    threshold: float
    time_window_minutes: int
    severity_filter: Optional[ErrorSeverity] = None
    category_filter: Optional[ErrorCategory] = None
    component_filter: Optional[str] = None
    enabled: bool = True
    cooldown_minutes: int = 60
    last_triggered: Optional[str] = None


class ErrorTracker:
    """
    Comprehensive error tracking and alerting system
    
    Features:
    - Error event collection and categorization
    - Automatic error classification
    - Alert rule engine
    - Error pattern detection
    - Recovery procedure automation
    - Error analytics and reporting
    """
    
    def __init__(self, data_dir: str = "/data"):
        """
        Initialize error tracking system
        
        Args:
            data_dir: Directory for storing error data
        """
        self.data_dir = data_dir
        self.errors_dir = os.path.join(data_dir, "errors")
        self.logger = logging.getLogger(__name__)
        
        # Ensure errors directory exists
        os.makedirs(self.errors_dir, exist_ok=True)
        
        # In-memory error storage for fast access
        self.recent_errors = deque(maxlen=1000)
        self.error_counts = defaultdict(int)
        self.error_patterns = defaultdict(list)
        
        # Alert rules and handlers
        self.alert_rules = []
        self.alert_handlers = []
        self.alert_history = deque(maxlen=500)
        
        # Recovery procedures
        self.recovery_procedures = {}
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Load existing data
        self._load_error_data()
        self._setup_default_alert_rules()
        self._setup_default_recovery_procedures()
        
        # Start background processing
        self.processing_thread = threading.Thread(target=self._background_processing, daemon=True)
        self.processing_thread.start()
        
        self.logger.info("Error tracking system initialized")
    
    def track_error(self, component: str, message: str, exception: Optional[Exception] = None,
                   severity: ErrorSeverity = ErrorSeverity.MEDIUM, 
                   category: Optional[ErrorCategory] = None,
                   context: Optional[Dict[str, Any]] = None) -> str:
        """
        Track an error event
        
        Args:
            component: Component where error occurred
            message: Error message
            exception: Exception object if available
            severity: Error severity level
            category: Error category (auto-detected if None)
            context: Additional context information
            
        Returns:
            Error event ID
        """
        timestamp = datetime.now(timezone.utc).isoformat()
        error_id = f"err_{int(time.time() * 1000)}"
        
        # Auto-detect category if not provided
        if category is None:
            category = self._classify_error(component, message, exception)
        
        # Extract stack trace if exception provided
        stack_trace = None
        if exception:
            stack_trace = traceback.format_exception(type(exception), exception, exception.__traceback__)
            stack_trace = ''.join(stack_trace)
        
        # Create error event
        error_event = ErrorEvent(
            id=error_id,
            timestamp=timestamp,
            severity=severity,
            category=category,
            component=component,
            message=message,
            details={
                "exception_type": type(exception).__name__ if exception else None,
                "exception_args": str(exception.args) if exception else None
            },
            stack_trace=stack_trace,
            context=context or {}
        )
        
        # Check for duplicate errors
        duplicate_id = self._check_for_duplicate(error_event)
        if duplicate_id:
            self._update_duplicate_error(duplicate_id, timestamp)
            return duplicate_id
        
        with self.lock:
            # Store error
            self.recent_errors.append(error_event)
            self.error_counts[f"{category.value}_{component}"] += 1
            
            # Update error patterns
            pattern_key = f"{category.value}_{severity.value}"
            self.error_patterns[pattern_key].append(timestamp)
            
            # Keep only recent patterns
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            cutoff_str = cutoff_time.isoformat()
            self.error_patterns[pattern_key] = [
                t for t in self.error_patterns[pattern_key] if t > cutoff_str
            ]
        
        # Persist error
        self._persist_error(error_event)
        
        # Check alert rules
        self._check_alert_rules(error_event)
        
        # Attempt automatic recovery
        self._attempt_recovery(error_event)
        
        self.logger.warning(f"Error tracked: {error_id} - {component}: {message}")
        return error_id
    
    def _classify_error(self, component: str, message: str, 
                       exception: Optional[Exception]) -> ErrorCategory:
        """Automatically classify error based on context"""
        message_lower = message.lower()
        component_lower = component.lower()
        
        # AI Analysis errors
        if any(keyword in message_lower for keyword in ['gemini', 'ai', 'analysis', 'model']):
            return ErrorCategory.AI_ANALYSIS
        
        # Home Assistant errors
        if any(keyword in message_lower for keyword in ['home assistant', 'ha', 'entity', 'service']):
            return ErrorCategory.HOME_ASSISTANT
        
        # Network errors
        if any(keyword in message_lower for keyword in ['connection', 'timeout', 'network', 'http']):
            return ErrorCategory.NETWORK
        
        # Configuration errors
        if any(keyword in message_lower for keyword in ['config', 'validation', 'missing']):
            return ErrorCategory.CONFIGURATION
        
        # Storage errors
        if any(keyword in message_lower for keyword in ['file', 'disk', 'storage', 'permission']):
            return ErrorCategory.STORAGE
        
        # Authentication errors
        if any(keyword in message_lower for keyword in ['auth', 'token', 'key', 'unauthorized']):
            return ErrorCategory.AUTHENTICATION
        
        # System errors
        if any(keyword in message_lower for keyword in ['memory', 'cpu', 'system', 'resource']):
            return ErrorCategory.SYSTEM
        
        return ErrorCategory.UNKNOWN
    
    def _check_for_duplicate(self, error_event: ErrorEvent) -> Optional[str]:
        """Check if this error is a duplicate of a recent error"""
        with self.lock:
            for existing_error in reversed(self.recent_errors):
                if (existing_error.component == error_event.component and
                    existing_error.category == error_event.category and
                    existing_error.message == error_event.message and
                    not existing_error.resolved):
                    
                    # Check if within duplicate time window (5 minutes)
                    existing_time = datetime.fromisoformat(existing_error.timestamp.replace('Z', '+00:00'))
                    current_time = datetime.fromisoformat(error_event.timestamp.replace('Z', '+00:00'))
                    
                    if (current_time - existing_time).total_seconds() < 300:  # 5 minutes
                        return existing_error.id
        
        return None
    
    def _update_duplicate_error(self, error_id: str, timestamp: str):
        """Update duplicate error with new occurrence"""
        with self.lock:
            for error in self.recent_errors:
                if error.id == error_id:
                    error.occurrence_count += 1
                    error.last_seen = timestamp
                    break
        
        # Update persisted error
        self._update_persisted_error(error_id, {"occurrence_count": "+1", "last_seen": timestamp})
    
    def add_alert_rule(self, rule: AlertRule):
        """Add a new alert rule"""
        with self.lock:
            self.alert_rules.append(rule)
        self.logger.info(f"Added alert rule: {rule.name}")
    
    def add_alert_handler(self, handler: Callable[[ErrorEvent, AlertRule], None]):
        """Add an alert handler function"""
        self.alert_handlers.append(handler)
        self.logger.info("Added alert handler")
    
    def _setup_default_alert_rules(self):
        """Setup default alert rules"""
        default_rules = [
            AlertRule(
                name="Critical Error Alert",
                condition="severity_threshold",
                threshold=1,
                time_window_minutes=1,
                severity_filter=ErrorSeverity.CRITICAL,
                cooldown_minutes=30
            ),
            AlertRule(
                name="High Error Rate",
                condition="error_rate",
                threshold=5,  # 5 errors per minute
                time_window_minutes=5,
                cooldown_minutes=15
            ),
            AlertRule(
                name="AI Analysis Failures",
                condition="error_count",
                threshold=3,
                time_window_minutes=10,
                category_filter=ErrorCategory.AI_ANALYSIS,
                cooldown_minutes=20
            ),
            AlertRule(
                name="Home Assistant Connection Issues",
                condition="error_count",
                threshold=5,
                time_window_minutes=15,
                category_filter=ErrorCategory.HOME_ASSISTANT,
                cooldown_minutes=30
            )
        ]
        
        for rule in default_rules:
            self.add_alert_rule(rule)
    
    def _setup_default_recovery_procedures(self):
        """Setup default recovery procedures"""
        self.recovery_procedures = {
            ErrorCategory.AI_ANALYSIS: self._recover_ai_analysis,
            ErrorCategory.HOME_ASSISTANT: self._recover_home_assistant,
            ErrorCategory.NETWORK: self._recover_network,
            ErrorCategory.CONFIGURATION: self._recover_configuration,
            ErrorCategory.STORAGE: self._recover_storage
        }
    
    def _check_alert_rules(self, error_event: ErrorEvent):
        """Check if any alert rules are triggered"""
        current_time = datetime.now(timezone.utc)
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # Check cooldown
            if rule.last_triggered:
                last_trigger_time = datetime.fromisoformat(rule.last_triggered.replace('Z', '+00:00'))
                if (current_time - last_trigger_time).total_seconds() < rule.cooldown_minutes * 60:
                    continue
            
            # Apply filters
            if rule.severity_filter and error_event.severity != rule.severity_filter:
                continue
            if rule.category_filter and error_event.category != rule.category_filter:
                continue
            if rule.component_filter and error_event.component != rule.component_filter:
                continue
            
            # Check condition
            if self._evaluate_alert_condition(rule, error_event):
                self._trigger_alert(rule, error_event)
                rule.last_triggered = current_time.isoformat()
    
    def _evaluate_alert_condition(self, rule: AlertRule, error_event: ErrorEvent) -> bool:
        """Evaluate if alert rule condition is met"""
        time_window = timedelta(minutes=rule.time_window_minutes)
        cutoff_time = datetime.now(timezone.utc) - time_window
        
        with self.lock:
            recent_errors = [
                err for err in self.recent_errors
                if datetime.fromisoformat(err.timestamp.replace('Z', '+00:00')) >= cutoff_time
            ]
            
            # Apply filters to recent errors
            filtered_errors = []
            for err in recent_errors:
                if rule.severity_filter and err.severity != rule.severity_filter:
                    continue
                if rule.category_filter and err.category != rule.category_filter:
                    continue
                if rule.component_filter and err.component != rule.component_filter:
                    continue
                filtered_errors.append(err)
        
        if rule.condition == "error_count":
            return len(filtered_errors) >= rule.threshold
        elif rule.condition == "error_rate":
            rate = len(filtered_errors) / rule.time_window_minutes
            return rate >= rule.threshold
        elif rule.condition == "severity_threshold":
            return len(filtered_errors) >= rule.threshold
        
        return False
    
    def _trigger_alert(self, rule: AlertRule, error_event: ErrorEvent):
        """Trigger an alert"""
        alert = {
            "rule_name": rule.name,
            "error_id": error_event.id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "severity": error_event.severity.value,
            "category": error_event.category.value,
            "component": error_event.component,
            "message": error_event.message
        }
        
        with self.lock:
            self.alert_history.append(alert)
        
        # Call alert handlers
        for handler in self.alert_handlers:
            try:
                handler(error_event, rule)
            except Exception as e:
                self.logger.error(f"Alert handler failed: {e}")
        
        self.logger.warning(f"Alert triggered: {rule.name} for error {error_event.id}")
    
    def _attempt_recovery(self, error_event: ErrorEvent):
        """Attempt automatic recovery for the error"""
        if error_event.category in self.recovery_procedures:
            try:
                recovery_func = self.recovery_procedures[error_event.category]
                recovery_result = recovery_func(error_event)
                
                if recovery_result:
                    self.logger.info(f"Recovery attempted for error {error_event.id}: {recovery_result}")
                    error_event.context["recovery_attempted"] = recovery_result
                    
            except Exception as e:
                self.logger.error(f"Recovery procedure failed for {error_event.id}: {e}")
    
    def _recover_ai_analysis(self, error_event: ErrorEvent) -> Optional[str]:
        """Recovery procedure for AI analysis errors"""
        # Clear AI cache
        try:
            cache_dir = os.path.join(self.data_dir, "cache")
            if os.path.exists(cache_dir):
                import shutil
                shutil.rmtree(cache_dir)
                os.makedirs(cache_dir, exist_ok=True)
                return "Cleared AI analysis cache"
        except Exception:
            pass
        
        return None
    
    def _recover_home_assistant(self, error_event: ErrorEvent) -> Optional[str]:
        """Recovery procedure for Home Assistant errors"""
        # Could implement HA connection retry logic
        return "HA connection retry scheduled"
    
    def _recover_network(self, error_event: ErrorEvent) -> Optional[str]:
        """Recovery procedure for network errors"""
        # Could implement network connectivity checks
        return "Network connectivity check scheduled"
    
    def _recover_configuration(self, error_event: ErrorEvent) -> Optional[str]:
        """Recovery procedure for configuration errors"""
        # Could implement configuration validation and reset
        return "Configuration validation scheduled"
    
    def _recover_storage(self, error_event: ErrorEvent) -> Optional[str]:
        """Recovery procedure for storage errors"""
        # Could implement disk cleanup
        return "Storage cleanup scheduled"
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get error summary for the specified time period"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        with self.lock:
            recent_errors = [
                err for err in self.recent_errors
                if datetime.fromisoformat(err.timestamp.replace('Z', '+00:00')) >= cutoff_time
            ]
        
        # Categorize errors
        by_category = defaultdict(int)
        by_severity = defaultdict(int)
        by_component = defaultdict(int)
        
        for error in recent_errors:
            by_category[error.category.value] += error.occurrence_count
            by_severity[error.severity.value] += error.occurrence_count
            by_component[error.component] += error.occurrence_count
        
        # Calculate error rate
        error_rate = len(recent_errors) / hours if hours > 0 else 0
        
        return {
            "period_hours": hours,
            "total_errors": len(recent_errors),
            "total_occurrences": sum(err.occurrence_count for err in recent_errors),
            "error_rate_per_hour": error_rate,
            "by_category": dict(by_category),
            "by_severity": dict(by_severity),
            "by_component": dict(by_component),
            "recent_alerts": list(self.alert_history)[-10:],
            "unresolved_errors": len([err for err in recent_errors if not err.resolved])
        }
    
    def resolve_error(self, error_id: str, resolution_notes: str = ""):
        """Mark an error as resolved"""
        with self.lock:
            for error in self.recent_errors:
                if error.id == error_id:
                    error.resolved = True
                    error.resolution_notes = resolution_notes
                    break
        
        self._update_persisted_error(error_id, {
            "resolved": True,
            "resolution_notes": resolution_notes
        })
        
        self.logger.info(f"Error {error_id} marked as resolved")
    
    def _persist_error(self, error_event: ErrorEvent):
        """Persist error to disk"""
        try:
            error_file = os.path.join(self.errors_dir, f"{error_event.id}.json")
            with open(error_file, 'w') as f:
                json.dump(asdict(error_event), f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Failed to persist error {error_event.id}: {e}")
    
    def _update_persisted_error(self, error_id: str, updates: Dict[str, Any]):
        """Update persisted error with new data"""
        try:
            error_file = os.path.join(self.errors_dir, f"{error_id}.json")
            if os.path.exists(error_file):
                with open(error_file, 'r') as f:
                    error_data = json.load(f)
                
                for key, value in updates.items():
                    if key == "occurrence_count" and value.startswith("+"):
                        error_data[key] = error_data.get(key, 0) + int(value[1:])
                    else:
                        error_data[key] = value
                
                with open(error_file, 'w') as f:
                    json.dump(error_data, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Failed to update persisted error {error_id}: {e}")
    
    def _load_error_data(self):
        """Load recent error data from disk"""
        try:
            # Load recent errors (last 24 hours)
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            for filename in os.listdir(self.errors_dir):
                if filename.endswith('.json') and filename.startswith('err_'):
                    try:
                        with open(os.path.join(self.errors_dir, filename), 'r') as f:
                            error_data = json.load(f)
                        
                        error_time = datetime.fromisoformat(error_data["timestamp"].replace('Z', '+00:00'))
                        if error_time >= cutoff_time:
                            # Convert back to ErrorEvent
                            error_data["severity"] = ErrorSeverity(error_data["severity"])
                            error_data["category"] = ErrorCategory(error_data["category"])
                            error_event = ErrorEvent(**error_data)
                            self.recent_errors.append(error_event)
                            
                    except Exception as e:
                        self.logger.error(f"Failed to load error file {filename}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Failed to load error data: {e}")
    
    def _background_processing(self):
        """Background thread for error processing and cleanup"""
        while True:
            try:
                time.sleep(300)  # Run every 5 minutes
                
                # Clean up old error files
                cutoff_time = datetime.now(timezone.utc) - timedelta(days=7)
                
                for filename in os.listdir(self.errors_dir):
                    if filename.endswith('.json') and filename.startswith('err_'):
                        file_path = os.path.join(self.errors_dir, filename)
                        file_time = datetime.fromtimestamp(os.path.getmtime(file_path), tz=timezone.utc)
                        
                        if file_time < cutoff_time:
                            os.remove(file_path)
                
            except Exception as e:
                self.logger.error(f"Error in background processing: {e}")
    
    def export_errors(self, hours: int = 24) -> Dict[str, Any]:
        """Export error data for external analysis"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        with self.lock:
            recent_errors = [
                asdict(err) for err in self.recent_errors
                if datetime.fromisoformat(err.timestamp.replace('Z', '+00:00')) >= cutoff_time
            ]
        
        return {
            "export_timestamp": datetime.now(timezone.utc).isoformat(),
            "period_hours": hours,
            "errors": recent_errors,
            "summary": self.get_error_summary(hours)
        }
