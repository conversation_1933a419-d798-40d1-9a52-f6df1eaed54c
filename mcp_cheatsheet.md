# MCP Server and Python Tool Cheatsheet for AugmentAI

This document provides a comprehensive guide to the MCP (Model Context Protocol) servers and Python libraries available in this environment.

## MCP Servers

You can interact with these servers using the `use_mcp_tool` function.

### 1. Filesystem (`filesystem`)

Provides tools for interacting with the local file system.

**Tools:**

*   `read_file(path: str)`: Reads the content of a file.
*   `write_file(path: str, content: str)`: Writes content to a file, overwriting it if it exists.
*   `list_directory(path: str)`: Lists the contents of a directory.
*   ...and more for moving, deleting, and getting file info.

**Example Usage:**

```
<use_mcp_tool>
  <server_name>filesystem</server_name>
  <tool_name>read_file</tool_name>
  <arguments>
    {
      "path": "path/to/your/file.txt"
    }
  </arguments>
</use_mcp_tool>
```

### 2. Notion (`notion-mcp-server`)

Provides tools for interacting with the Notion API.

**Tools:**

*   `notion_pages`: Create, update, archive, restore, and search pages.
*   `notion_blocks`: Retrieve, append, update, and delete blocks.
*   `notion_database`: Create, query, and update databases.
*   `notion_comments`: Get, create, and reply to comments.
*   `notion_users`: List and get user information.

**Example Usage:**

```
<use_mcp_tool>
  <server_name>notion-mcp-server</server_name>
  <tool_name>notion_pages</tool_name>
  <arguments>
    {
      "payload": {
        "action": "create_page",
        "params": {
          "parent": { "page_id": "YOUR_PAGE_ID" },
          "properties": {
            "title": {
              "title": [{ "text": { "content": "New Page Title" } }]
            }
          }
        }
      }
    }
  </arguments>
</use_mcp_tool>
```

### 3. Sequential Thinking (`sequential-thinking`)

A tool for breaking down complex problems into a series of thoughts.

**Tool:**

*   `sequentialthinking`: Guides a step-by-step thinking process.

**Example Usage:**

```
<use_mcp_tool>
  <server_name>sequential-thinking</server_name>
  <tool_name>sequentialthinking</tool_name>
  <arguments>
    {
      "thought": "First, I need to understand the user's request.",
      "nextThoughtNeeded": true,
      "thoughtNumber": 1,
      "totalThoughts": 5
    }
  </arguments>
</use_mcp_tool>
```

### 4. Memory (`memory`)

Provides tools for creating and managing a knowledge graph.

**Tools:**

*   `create_entities`: Create new entities in the graph.
*   `create_relations`: Create relationships between entities.
*   `add_observations`: Add observations to existing entities.
*   `search_nodes`: Search for nodes in the graph.

**Example Usage:**

```
<use_mcp_tool>
  <server_name>memory</server_name>
  <tool_name>create_entities</tool_name>
  <arguments>
    {
      "entities": [
        {
          "name": "Home Assistant",
          "entityType": "Software",
          "observations": ["An open-source home automation platform."]
        }
      ]
    }
  </arguments>
</use_mcp_tool>
```

### 5. Everything (`everything`)

A general-purpose server for testing and demonstration.

**Tools:**

*   `echo(message: str)`: Echoes back the input message.
*   `add(a: number, b: number)`: Adds two numbers.
*   ...and more for demonstrating various MCP features.

**Example Usage:**

```
<use_mcp_tool>
  <server_name>everything</server_name>
  <tool_name>echo</tool_name>
  <arguments>
    {
      "message": "Hello, world!"
    }
  </arguments>
</use_mcp_tool>
```

### 6. Conport (`conport`)

Provides tools for managing project context, decisions, and progress.

**Tools:**

*   `get_product_context`: Retrieves overall project goals.
*   `log_decision`: Logs an architectural or implementation decision.
*   `log_progress`: Logs a progress entry or task status.

**Example Usage:**

```
<use_mcp_tool>
  <server_name>conport</server_name>
  <tool_name>log_decision</tool_name>
  <arguments>
    {
      "workspace_id": "${workspaceFolder}",
      "summary": "Use MCP servers for extensibility.",
      "rationale": "Allows for adding new tools and capabilities without modifying the core application."
    }
  </arguments>
</use_mcp_tool>
```

---

## Python Libraries

These libraries are installed in the Python environment and can be used in any Python script.

### 1. Pydantic (`pydantic`)

A data validation and settings management library using Python type annotations.

*   **Usage Example:**

```python
from pydantic import BaseModel, Field
from typing import List

class ServerConfig(BaseModel):
    name: str
    port: int = Field(8000, gt=0)
    tags: List[str] = []

# Example instantiation
config = ServerConfig(name="MyServer", tags=["production"])
print(config.model_dump_json(indent=2))
```

### 2. Web Crawler (`crawl4ai`)

An asynchronous web crawling library designed for AI applications.

*   **Key Class:** `crawl4ai.AsyncWebCrawler`
*   **Core Method:** `arun(url: str)`
*   **Important:** The library is asynchronous. All calls must be handled within an `async` function and executed with `asyncio.run()`.

*   **Usage Example:**

```python
import asyncio
from crawl4ai import AsyncWebCrawler

async def fetch_website_content(target_url: str):
    """
    Crawls a single URL and prints the extracted markdown content.
    """
    async with AsyncWebCrawler() as crawler:
        result_container = await crawler.arun(url=target_url)
        if result_container.success and result_container.markdown:
            print(f"URL: {result_container.url}")
            content_preview = result_container.markdown.raw_markdown[:500]
            print(f"Content Preview (first 500 chars):\n{content_preview}...")

if __name__ == "__main__":
    asyncio.run(fetch_website_content("https://modelcontextprotocol.io"))