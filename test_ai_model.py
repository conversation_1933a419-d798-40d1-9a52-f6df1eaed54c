#!/usr/bin/env python3
"""
Test script to verify user-configurable AI model selection in AICleaner
"""
import sys
import os
sys.path.append('/root/addons/Aiclean')

from aicleaner.aicleaner import AICleaner
from PIL import Image
import tempfile

def test_ai_model_configuration(gemini_model='flash'):
    """Test AI model configuration with user preference"""
    print(f"🧪 Testing AI Model Configuration (User Preference: {gemini_model})...")

    try:
        import json
        import shutil

        # Backup original options.json
        options_path = '/data/options.json'
        backup_path = '/data/options.json.backup'

        if os.path.exists(options_path):
            shutil.copy2(options_path, backup_path)

            # Load current options
            with open(options_path, 'r') as f:
                options = json.load(f)
        else:
            # Create minimal options if none exist
            options = {
                'gemini_api_key': 'test_key_for_service_integration_testing',
                'display_name': 'Test User',
                'enable_multi_model_ai': False,
                'zones': [{
                    'name': 'kitchen',
                    'icon': 'mdi:chef-hat',
                    'purpose': 'Test kitchen zone',
                    'camera_entity': 'camera.kitchen',
                    'todo_list_entity': 'todo.kitchen_tasks',
                    'update_frequency': 30,
                    'notifications_enabled': False,
                    'notification_service': '',
                    'notification_personality': 'default',
                    'notify_on_create': False,
                    'notify_on_complete': False
                }]
            }

        # Set the gemini_model preference
        options['gemini_model'] = gemini_model

        # Write updated options
        with open(options_path, 'w') as f:
            json.dump(options, f, indent=2)

        # Initialize AICleaner (it will load from /data/options.json)
        print("📝 Initializing AICleaner...")
        print(f"🔧 Set gemini_model in options.json to: {gemini_model}")
        aicleaner = AICleaner()

        # Debug: Check what configuration was actually loaded
        actual_config = getattr(aicleaner, 'config', {})
        actual_gemini_model = actual_config.get('gemini_model', 'NOT_SET')
        print(f"🔍 AICleaner loaded gemini_model: {actual_gemini_model}")

        # Check the Gemini client model
        if hasattr(aicleaner, 'gemini_client') and aicleaner.gemini_client:
            model_name = aicleaner.gemini_client._model_name
            print(f"🤖 Gemini Client Model: {model_name}")

            # Verify the model matches user preference
            expected_model = f"models/gemini-1.5-{gemini_model}"
            if model_name == expected_model:
                print(f"✅ SUCCESS: Using {model_name} (matches user preference: {gemini_model})")
                result = True
            else:
                print(f"❌ MISMATCH: Expected {expected_model}, got {model_name}")
                result = False
        else:
            print("❌ ERROR: No Gemini client found")
            result = False

    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        result = False
    finally:
        # Restore original options.json
        try:
            if os.path.exists(backup_path):
                shutil.move(backup_path, options_path)
                print("🔄 Restored original options.json")
        except Exception as e:
            print(f"⚠️  Warning: Could not restore options.json: {e}")

    return result

def test_both_models():
    """Test both Flash and Pro model configurations"""
    print("🔬 Testing User-Configurable AI Model Selection")
    print("=" * 50)

    # Test Flash model (default)
    print("\n1️⃣ Testing Flash Model Configuration:")
    flash_success = test_ai_model_configuration('flash')

    print("\n2️⃣ Testing Pro Model Configuration:")
    pro_success = test_ai_model_configuration('pro')

    print("\n📊 Test Results:")
    print(f"   Flash Model: {'✅ PASS' if flash_success else '❌ FAIL'}")
    print(f"   Pro Model:   {'✅ PASS' if pro_success else '❌ FAIL'}")

    if flash_success and pro_success:
        print("\n🎉 SUCCESS: User-configurable AI model selection is working!")
        return True
    else:
        print("\n❌ FAILURE: User-configurable AI model selection has issues")
        return False

if __name__ == "__main__":
    success = test_both_models()
    sys.exit(0 if success else 1)
