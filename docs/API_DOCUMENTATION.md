# AICleaner v2.0 API Documentation

Complete API reference for AICleaner v2.0 services, sensors, and integration endpoints.

## 🚀 Service Endpoints

### `aicleaner.run_analysis`

Triggers analysis for all zones or a specific zone using the traditional 3-call method.

**Service Data:**
```yaml
service: aicleaner.run_analysis
data:
  zone_name: "Kitchen"  # Optional: specific zone name
```

**Parameters:**
- `zone_name` (optional): Target specific zone. If omitted, analyzes all zones.

**Response Time:** ~3-5 seconds per zone
**Use Case:** Standard analysis when caching is not critical

---

### `aicleaner.run_optimized_analysis`

Triggers optimized batch analysis with 40%+ performance improvement and intelligent caching.

**Service Data:**
```yaml
service: aicleaner.run_optimized_analysis
data:
  zone_name: "Kitchen"  # Optional: specific zone name
```

**Parameters:**
- `zone_name` (optional): Target specific zone. If omitted, analyzes all zones.

**Response Time:** ~2-3 seconds per zone (40%+ faster)
**Cache Performance:** 0.0002s for cached results (12,481x faster)
**Use Case:** Production environments, frequent analysis, performance-critical applications

---

### `aicleaner.clear_cache`

Clears the AI analysis cache for fresh results.

**Service Data:**
```yaml
service: aicleaner.clear_cache
```

**Use Case:** Force fresh analysis, troubleshooting, cache management

---

## 📊 Sensor Entities

### Zone Status Sensors

Each configured zone creates the following sensors:

#### `sensor.aicleaner_{zone_name}_status`

**Attributes:**
```json
{
  "state": "active",
  "zone_name": "Kitchen",
  "last_analysis": "2025-06-29T09:42:00Z",
  "active_tasks": 3,
  "completed_tasks": 2,
  "cleanliness_score": 7,
  "analysis_time": 2.87,
  "cache_hit": false,
  "next_scheduled": "2025-06-30T09:42:00Z"
}
```

**State Values:**
- `active`: Zone is operational and scheduled
- `analyzing`: Currently performing analysis
- `error`: Error in last analysis
- `disabled`: Zone is disabled

---

#### `sensor.aicleaner_{zone_name}_cleanliness`

**Attributes:**
```json
{
  "state": 7,
  "unit_of_measurement": "score",
  "friendly_name": "Kitchen Cleanliness Score",
  "cleanliness_state": "moderately_clean",
  "observations": [
    "Surfaces are mostly clean",
    "Some clutter visible on counter"
  ],
  "recommendations": [
    "Wipe down countertops",
    "Organize loose items"
  ],
  "improvement_areas": [
    "Kitchen counter",
    "Sink area"
  ],
  "last_updated": "2025-06-29T09:42:00Z"
}
```

**Score Range:** 0-10 (0 = very messy, 10 = perfectly clean)

---

#### `sensor.aicleaner_{zone_name}_tasks`

**Attributes:**
```json
{
  "state": 5,
  "unit_of_measurement": "tasks",
  "friendly_name": "Kitchen Active Tasks",
  "active_tasks": [
    {
      "id": "task_001",
      "description": "Wipe down countertops",
      "priority": 8,
      "category": "cleaning",
      "created": "2025-06-29T09:42:00Z"
    }
  ],
  "completed_today": 2,
  "total_created": 15,
  "completion_rate": 80.5
}
```

---

### System Performance Sensors

#### `sensor.aicleaner_performance`

**Attributes:**
```json
{
  "state": "optimal",
  "average_analysis_time": 2.87,
  "cache_hit_rate": 65.2,
  "total_analyses": 150,
  "successful_analyses": 148,
  "error_rate": 1.3,
  "zones_analyzed": 3,
  "last_optimization": "2025-06-29T09:42:00Z"
}
```

---

## 🔄 Webhook Endpoints

### Analysis Trigger Webhook

**Endpoint:** `POST /api/webhook/aicleaner_trigger`

**Headers:**
```
Content-Type: application/json
Authorization: Bearer {webhook_token}
```

**Payload:**
```json
{
  "zone_name": "Kitchen",
  "force_analysis": true,
  "use_optimization": true
}
```

**Response:**
```json
{
  "success": true,
  "analysis_id": "analysis_12345",
  "estimated_completion": "2025-06-29T09:45:00Z"
}
```

---

## 📱 Notification Formats

### Task Completion Notification

```json
{
  "title": "🎉 Tasks Completed!",
  "message": "Kitchen: 2 tasks completed\n✅ Wipe down countertops\n✅ Put away dishes",
  "data": {
    "zone_name": "Kitchen",
    "completed_tasks": 2,
    "cleanliness_improvement": 1.5,
    "actions": [
      {
        "action": "view_zone",
        "title": "View Zone"
      }
    ]
  }
}
```

### New Tasks Notification

```json
{
  "title": "📋 New Cleaning Tasks",
  "message": "Kitchen needs attention (Score: 6/10)\n• Organize spice rack\n• Clean microwave",
  "data": {
    "zone_name": "Kitchen",
    "new_tasks": 2,
    "cleanliness_score": 6,
    "priority_level": "medium"
  }
}
```

---

## 🔧 Configuration API

### Zone Configuration Format

```yaml
zones:
  - name: "Kitchen"
    camera_entity: "camera.kitchen_cam"
    todo_entity: "todo.kitchen_cleaning"
    notify_entity: "notify.mobile_app_phone"
    schedule_hours: 24
    purpose: "Keep kitchen clean and organized"
    ignore_rules:
      - "decorative items"
      - "personal belongings"
    notification_settings:
      personality: "friendly"
      task_threshold: 3
      cleanliness_threshold: 5
```

### Global Configuration

```yaml
gemini_api_key: "AIza..."
ha_url: "http://supervisor/core"
ha_token: "eyJ0..."
cache_ttl: 300
performance_optimization: true
error_recovery: true
logging_level: "INFO"
```

---

## 📈 Performance Metrics API

### Cache Statistics

**Endpoint:** Internal method `ai_optimizer.get_cache_stats()`

**Response:**
```json
{
  "total_entries": 15,
  "valid_entries": 12,
  "expired_entries": 3,
  "cache_ttl_seconds": 300,
  "hit_rate_percent": 65.2,
  "average_response_time": 0.0002
}
```

### Analysis Performance

**Response:**
```json
{
  "timestamp": "2025-06-29T09:42:00Z",
  "zones_analyzed": 3,
  "zone_results": [
    {
      "zone_name": "Kitchen",
      "success": true,
      "analysis_time": 2.87,
      "cache_hit": false,
      "completed_tasks_count": 2,
      "new_tasks_count": 1,
      "cleanliness_score": 7
    }
  ],
  "performance_metrics": {
    "total_analysis_time": 8.45,
    "average_zone_time": 2.82,
    "successful_zones": 3,
    "failed_zones": 0,
    "cache_hit_rate": 33.3,
    "zones_per_second": 0.35
  }
}
```

---

## 🛡️ Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "ANALYSIS_FAILED",
    "message": "Camera snapshot failed",
    "details": "Camera entity camera.kitchen_cam is unavailable",
    "timestamp": "2025-06-29T09:42:00Z",
    "zone_name": "Kitchen"
  },
  "fallback_used": true,
  "retry_suggested": true
}
```

### Common Error Codes

- `CAMERA_UNAVAILABLE`: Camera entity not accessible
- `API_TIMEOUT`: Gemini API request timeout
- `INVALID_RESPONSE`: AI analysis returned invalid data
- `CONFIG_ERROR`: Configuration validation failed
- `NETWORK_ERROR`: Network connectivity issues
- `RATE_LIMITED`: API rate limit exceeded

---

## 🔍 Debugging & Monitoring

### Log Levels

- `DEBUG`: Detailed analysis steps and cache operations
- `INFO`: Analysis results and performance metrics
- `WARNING`: Non-critical errors and fallback usage
- `ERROR`: Critical failures requiring attention

### Health Check Endpoint

**Endpoint:** `GET /api/aicleaner/health`

**Response:**
```json
{
  "status": "healthy",
  "version": "2.0.0",
  "zones_configured": 3,
  "zones_active": 3,
  "last_analysis": "2025-06-29T09:42:00Z",
  "cache_status": "optimal",
  "api_connectivity": "connected"
}
```

---

*For more detailed examples and integration guides, see the [Complete Documentation](README.md).*
