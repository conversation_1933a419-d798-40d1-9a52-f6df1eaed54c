# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having sub-dependencies with platform-specific packages, it may be better to ignore
#   Pipfile.lock.
# Pipfile.lock

# PEP 582; used by PDM, PEP 582 compatible installers
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static analyzer
.pytype/

# Cython debug symbols
cython_debug/

# VSCode
.vscode/

# Home Assistant Addon Development
# =================================

# Environment files (keep .env.mcp for MCP configuration)
.env
.env.local
.env.development
.env.test
.env.production
.env.secrets
!.env.mcp
!.env.secrets.template

# Home Assistant specific
secrets.yaml
known_devices.yaml
*.db
*.db-journal

# Logs and temporary files
logs/*.log
*.tmp
*.temp

# Screenshots and media (keep directory structure)
screenshots/*.png
screenshots/*.jpg
screenshots/*.jpeg
!screenshots/.gitkeep

# Archive directories (keep for reference)
.archive_*/

# Node.js (for JavaScript utilities)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# MCP Server files
mcp-config.json.backup
*.mcp.log

# Temporary script files
analyze_*.sh
cleanup_*.sh
organize_*.sh
review_*.sh
populate_*.sh
fix_*.sh
update_*.sh
final_*.sh
*_temp.sh

# Notion API temporary files
*_info.json
*_content.json
*_entries.json
current_blocks.json
existing_*.json
*_entry.json
corrected_content.json
new_content.json
main_content.json
additional_content.json
api_doc_fixed.json
task_entry.json
endpoint_entry.json
config_entry.json
idea_entry.json

# Backup files
*.bak
*.backup
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~
.idea/
*.sublime-project
*.sublime-workspace

# Project-specific ignores
# ========================
# Remove outdated entries - these files no longer exist or are needed:
# - DesignDocument.md (now needed)
# - pytest.ini (now needed)
# - tests/ (now needed)
# - Old project files removed during cleanup
