
"""Initial schema

Revision ID: 20250617
Revises:
Create Date: 2025-06-17 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import json

# revision identifiers, used by Alembic.
revision = '20250617'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto-generated by Alembic - please adjust! ###
    op.create_table('active_context',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('active_context_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('version', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('change_source', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('context_links',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workspace_id', sa.String(length=1024), nullable=False),
    sa.Column('source_item_type', sa.String(length=255), nullable=False),
    sa.Column('source_item_id', sa.String(length=255), nullable=False),
    sa.Column('target_item_type', sa.String(length=255), nullable=False),
    sa.Column('target_item_id', sa.String(length=255), nullable=False),
    sa.Column('relationship_type', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_context_links_source_item_id'), 'context_links', ['source_item_id'], unique=False)
    op.create_index(op.f('ix_context_links_source_item_type'), 'context_links', ['source_item_type'], unique=False)
    op.create_index(op.f('ix_context_links_target_item_id'), 'context_links', ['target_item_id'], unique=False)
    op.create_index(op.f('ix_context_links_target_item_type'), 'context_links', ['target_item_type'], unique=False)
    op.create_table('custom_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('category', sa.String(length=255), nullable=False),
    sa.Column('key', sa.String(length=255), nullable=False),
    sa.Column('value', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('category', 'key')
    )
    op.create_table('decisions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('summary', sa.Text(), nullable=False),
    sa.Column('rationale', sa.Text(), nullable=True),
    sa.Column('implementation_details', sa.Text(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('product_context',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('product_context_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('version', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('change_source', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('progress_entries',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['progress_entries.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('system_patterns',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    
    # Seed initial data
    op.execute("INSERT INTO product_context (id, content) VALUES (1, '{}')")
    op.execute("INSERT INTO active_context (id, content) VALUES (1, '{}')")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto-generated by Alembic - please adjust! ###
    op.drop_table('system_patterns')
    op.drop_table('progress_entries')
    op.drop_table('product_context_history')
    op.drop_table('product_context')
    op.drop_table('decisions')
    op.drop_table('custom_data')
    op.drop_index(op.f('ix_context_links_target_item_type'), table_name='context_links')
    op.drop_index(op.f('ix_context_links_target_item_id'), table_name='context_links')
    op.drop_index(op.f('ix_context_links_source_item_type'), table_name='context_links')
    op.drop_index(op.f('ix_context_links_source_item_id'), table_name='context_links')
    op.drop_table('context_links')
    op.drop_table('active_context_history')
    op.drop_table('active_context')
    # ### end Alembic commands ###
