# AICleaner MQTT Implementation Plan

## Executive Summary

This document outlines the comprehensive migration plan for transitioning the AICleaner Home Assistant addon from REST API-based entity management to native MQTT discovery protocol. This migration will resolve persistent entity issues, eliminate service registration complexity, and provide a more robust, scalable architecture.

## Current Architecture Issues

### Problems with REST API Approach
- **Temporary Entity Creation**: `/api/states` endpoint creates temporary state dictionaries, not real entities
- **Entity Persistence Issues**: Entities disappear after Home Assistant restarts
- **Service Registration Complexity**: Required Flask webhook workaround for service calls
- **State Contamination**: Persistent state files accumulate obsolete zone data
- **Manual Cleanup Required**: Unavailable sensors remain in entity registry
- **Complex Template Sensors**: Required template trigger sensors to convert temporary states to real entities

### Benefits of MQTT Migration
- **Native Entity Discovery**: MQTT discovery creates real, persistent Home Assistant entities
- **Automatic Lifecycle Management**: Entities can be cleanly removed via empty config payloads
- **No Service Registration**: MQTT buttons handle actions directly without custom services
- **Simplified Architecture**: Eliminates Flask webhook, template sensors, and trigger files
- **Better Performance**: Direct MQTT communication vs HTTP API calls
- **Standardized Protocol**: Follows Home Assistant's preferred integration pattern

## MQTT Architecture Design

### Topic Structure
Following Home Assistant MQTT discovery convention:
```
homeassistant/{component}/{node_id}/{object_id}/config
homeassistant/{component}/{node_id}/{object_id}/state
homeassistant/{component}/{node_id}/{object_id}/command
homeassistant/{component}/{node_id}/{object_id}/availability
```

### Device Configuration
```json
{
  "device": {
    "identifiers": ["aicleaner_addon"],
    "name": "AICleaner",
    "model": "Home Assistant Addon",
    "manufacturer": "AICleaner Project",
    "sw_version": "1.0.0",
    "configuration_url": "http://homeassistant.local:8123/hassio/addon/local_aicleaner"
  },
  "origin": {
    "name": "AICleaner",
    "sw_version": "1.0.0",
    "support_url": "https://github.com/user/aicleaner"
  }
}
```

### Entity Types and Topics

#### 1. System Status Sensor
- **Config Topic**: `homeassistant/sensor/aicleaner/system_status/config`
- **State Topic**: `aicleaner/system/status`
- **Purpose**: Overall addon health and operational status

#### 2. Zone Task Sensors (per zone)
- **Config Topic**: `homeassistant/sensor/aicleaner/{zone_id}_tasks/config`
- **State Topic**: `aicleaner/zones/{zone_id}/tasks`
- **Purpose**: Active task count and task list data

#### 3. Zone Cleanliness Sensors (per zone)
- **Config Topic**: `homeassistant/sensor/aicleaner/{zone_id}_cleanliness/config`
- **State Topic**: `aicleaner/zones/{zone_id}/cleanliness`
- **Purpose**: AI-analyzed cleanliness score and recommendations

#### 4. Analyze Button (per zone)
- **Config Topic**: `homeassistant/button/aicleaner/{zone_id}_analyze/config`
- **Command Topic**: `aicleaner/zones/{zone_id}/analyze/command`
- **Purpose**: Trigger AI analysis for specific zone

#### 5. Camera View Button (per zone)
- **Config Topic**: `homeassistant/button/aicleaner/{zone_id}_camera/config`
- **Command Topic**: `aicleaner/zones/{zone_id}/camera/command`
- **Purpose**: Open camera view modal

#### 6. AI Model Select
- **Config Topic**: `homeassistant/select/aicleaner/ai_model/config`
- **State Topic**: `aicleaner/config/ai_model`
- **Command Topic**: `aicleaner/config/ai_model/set`
- **Options**: `["flash", "pro"]`
- **Purpose**: User-configurable AI model selection

### Availability Management
- **Availability Topic**: `aicleaner/availability`
- **Online Payload**: `"online"`
- **Offline Payload**: `"offline"`
- **Birth Message**: Published on MQTT connect with retain=true
- **Last Will**: Configured to publish "offline" on disconnect

## Implementation Strategy

### Phase 1: Parallel Implementation (Week 1)
1. **Add MQTT Dependencies**
   - Install `paho-mqtt` Python library
   - Add MQTT configuration to addon options
   - Create MQTT client wrapper class

2. **Implement MQTT Publisher**
   - Create `MQTTPublisher` class in new file `mqtt_client.py`
   - Implement discovery payload generation
   - Add entity lifecycle management methods
   - Implement availability and birth/last will messages

3. **Maintain Existing REST System**
   - Keep current REST API entities functional
   - Run both systems in parallel for validation
   - Add configuration flag to enable/disable MQTT

### Phase 2: MQTT Entity Creation (Week 2)
1. **System Status Sensor**
   - Publish discovery config on startup
   - Update state every 30 seconds
   - Include addon version, uptime, zone count in attributes

2. **Zone-Based Entities**
   - Dynamic discovery based on configured zones
   - Publish task sensors with proper device_class and unit_of_measurement
   - Publish cleanliness sensors with percentage values
   - Create analyze and camera buttons for each zone

3. **AI Model Select Entity**
   - Publish select entity with flash/pro options
   - Subscribe to command topic for model changes
   - Update internal configuration when changed

### Phase 3: Command Handling (Week 3)
1. **MQTT Command Subscriptions**
   - Subscribe to all button command topics
   - Subscribe to AI model select command topic
   - Implement command routing and processing

2. **Action Integration**
   - Connect analyze button to existing analysis workflow
   - Connect camera button to snapshot functionality
   - Connect AI model select to configuration updates

3. **State Synchronization**
   - Ensure MQTT state reflects actual addon state
   - Handle configuration changes from both MQTT and addon options
   - Implement proper error handling and logging

## Technical Implementation Details

### MQTT Client Configuration
```python
class MQTTClient:
    def __init__(self, broker_host, broker_port=1883, username=None, password=None):
        self.client = mqtt.Client()
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.device_info = self._get_device_info()
        
    def connect(self):
        # Set last will message
        self.client.will_set("aicleaner/availability", "offline", retain=True)
        self.client.connect(self.broker_host, self.broker_port, 60)
        
        # Publish birth message
        self.client.publish("aicleaner/availability", "online", retain=True)
```

### Discovery Payload Examples

#### Zone Task Sensor
```json
{
  "name": "Rowan's Room Tasks",
  "unique_id": "aicleaner_rowans_room_tasks",
  "state_topic": "aicleaner/zones/rowans_room/tasks",
  "unit_of_measurement": "tasks",
  "icon": "mdi:format-list-checks",
  "device_class": null,
  "state_class": "measurement",
  "availability_topic": "aicleaner/availability",
  "device": { /* device info */ },
  "origin": { /* origin info */ }
}
```

#### Analyze Button
```json
{
  "name": "Analyze Rowan's Room",
  "unique_id": "aicleaner_rowans_room_analyze",
  "command_topic": "aicleaner/zones/rowans_room/analyze/command",
  "payload_press": "ANALYZE",
  "device_class": "update",
  "icon": "mdi:robot",
  "availability_topic": "aicleaner/availability",
  "device": { /* device info */ },
  "origin": { /* origin info */ }
}
```

### Code Changes Required

#### 1. New Files
- `aicleaner/mqtt_client.py` - MQTT client wrapper and discovery management
- `aicleaner/mqtt_entities.py` - Entity configuration templates and payloads

#### 2. Modified Files
- `aicleaner/aicleaner.py` - Integration of MQTT client, parallel operation mode
- `aicleaner/config.yaml` - Add MQTT broker configuration options
- `requirements.txt` - Add paho-mqtt dependency

#### 3. Configuration Schema Updates
```yaml
mqtt_broker_host:
  description: "MQTT broker hostname or IP address"
  type: str
  default: "localhost"
mqtt_broker_port:
  description: "MQTT broker port"
  type: int
  default: 1883
mqtt_username:
  description: "MQTT broker username (optional)"
  type: str
  required: false
mqtt_password:
  description: "MQTT broker password (optional)"
  type: str
  required: false
enable_mqtt:
  description: "Enable MQTT discovery (experimental)"
  type: bool
  default: false
```

## Migration and Testing Plan

### Testing Phase (Week 4)
1. **Entity Discovery Verification**
   - Confirm all entities appear in Home Assistant
   - Verify proper device grouping and identification
   - Test entity availability states

2. **State Update Testing**
   - Verify sensor states update correctly
   - Test button functionality
   - Validate AI model selection changes

3. **Performance Comparison**
   - Compare MQTT vs REST response times
   - Monitor memory usage and CPU impact
   - Test reliability under load

### Full Migration (Week 5)
1. **Deprecation Warnings**
   - Add warnings about REST API deprecation
   - Provide migration timeline to users
   - Update documentation

2. **Default Switch**
   - Change default configuration to enable MQTT
   - Keep REST API as fallback option
   - Monitor for issues and user feedback

3. **Cleanup Phase**
   - Remove template trigger sensors
   - Disable Flask webhook service
   - Clean up trigger file system
   - Remove obsolete REST API code

## Rollback Strategy

### Immediate Rollback
- Configuration option to disable MQTT and re-enable REST
- Automatic fallback if MQTT broker unavailable
- Preserve existing entity IDs during transition

### Data Preservation
- Maintain entity history during migration
- Backup current state files before cleanup
- Provide migration utility for manual rollback

## Success Metrics

### Technical Metrics
- Zero entity persistence issues after Home Assistant restart
- Elimination of Flask webhook dependency
- Reduction in codebase complexity (estimated 30% fewer lines)
- Improved response times for button actions

### User Experience Metrics
- Seamless entity discovery without manual configuration
- Consistent entity availability across restarts
- Simplified troubleshooting and debugging
- Better integration with Home Assistant ecosystem

## Timeline Summary

- **Week 1**: Parallel MQTT implementation
- **Week 2**: Entity creation and discovery
- **Week 3**: Command handling and integration
- **Week 4**: Testing and validation
- **Week 5**: Full migration and cleanup

## Next Steps for Implementation Agent

1. **Start with Phase 1**: Implement MQTT client and parallel operation
2. **Focus on Zone Task Sensors**: Begin with most critical entities
3. **Test Incrementally**: Validate each entity type before proceeding
4. **Monitor Performance**: Compare MQTT vs REST throughout development
5. **Document Changes**: Update README and configuration documentation

This plan provides a comprehensive roadmap for migrating AICleaner to a robust, native MQTT architecture that aligns with Home Assistant best practices and eliminates current architectural limitations.
